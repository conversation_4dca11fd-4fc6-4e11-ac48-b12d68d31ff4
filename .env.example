# OmniParse 配置文件示例
# 复制此文件为 .env 并根据需要修改配置

# 模型和缓存目录配置
MODEL_DIR=/OmniParse
LAYOUTREADER_DIR=/OmniParse/layoutreader
PDF_OUTPUT_DIR=/OmniParse/pdf_output
MODELSCOPE_CACHE=/OmniParse/.cache
MINERU_TOOLS_CONFIG_JSON=/OmniParse/magic-pdf.json
FTLANG_CACHE=/OmniParse/ftlang_cache

# 服务配置
UVICORN_WORKERS=1

# 功能模块开关
ENABLE_PARSE_PDF=false
ENABLE_PARSE_AUDIO=true
ENABLE_DETECT_AIGC=true

# GPU配置
PDF_GPU_IDS=1
AUDIO_GPU_IDS=2
CUDA_VISIBLE_DEVICES=0,1,2,3

# 视频处理系统配置
# 字幕处理配置
SUBTITLE_AUTO_GENERATION_ENABLED=false  # 是否启用字幕自动生成（本地开发环境建议关闭）
SUBTITLE_LOCAL_SERVICE_ENABLED=false    # 是否启用本地音频服务生成字幕

# Xinference 配置
XINFERENCE_ENABLED=false                 # 是否启用 Xinference（本地开发环境建议关闭）
XINFERENCE_BASE_URL=http://localhost:9997
XINFERENCE_API_KEY=                      # API 密钥（可选）

# 视觉模型配置（第三方服务）
# 硅基流动
SILICONFLOW_API_KEY=                     # 硅基流动 API 密钥
SILICONFLOW_BASE_URL=https://api.siliconflow.cn/v1

# 智谱 GLM
ZHIPU_API_KEY=                           # 智谱 API 密钥
ZHIPU_BASE_URL=https://open.bigmodel.cn/api/paas/v4

# 火山引擎
VOLCENGINE_API_KEY=cf674da2-e1f2-4781-ae74-687a8b5a22c4  # 火山引擎 API 密钥
VOLCENGINE_BASE_URL=https://ark.cn-beijing.volces.com/api/v3
