#!/usr/bin/env python3
"""
测试短剧分类功能
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.config.drama_categories import get_drama_genres, get_drama_genre_groups, get_drama_themes

def test_drama_categories():
    """测试短剧分类配置"""
    print("=== 测试短剧分类配置 ===")
    
    # 测试类型
    genres = get_drama_genres()
    print(f"总共有 {len(genres)} 个短剧类型")
    print("前10个类型:", genres[:10])
    
    # 测试分组
    genre_groups = get_drama_genre_groups()
    print(f"\n总共有 {len(genre_groups)} 个分组")
    for group_name, group_genres in genre_groups.items():
        print(f"{group_name}: {len(group_genres)} 个类型")
    
    # 测试主题
    themes = get_drama_themes()
    print(f"\n总共有 {len(themes)} 个常用主题")
    print("前10个主题:", themes[:10])
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_drama_categories()
