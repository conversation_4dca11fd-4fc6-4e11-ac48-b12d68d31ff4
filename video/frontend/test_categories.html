<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>短剧分类测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .category-group { margin-bottom: 20px; }
        .category-group h3 { color: #333; border-bottom: 1px solid #ccc; padding-bottom: 5px; }
        .category-list { display: flex; flex-wrap: wrap; gap: 8px; margin-top: 10px; }
        .category-item { 
            background: #f0f0f0; 
            padding: 4px 8px; 
            border-radius: 4px; 
            font-size: 12px; 
        }
        .stats { background: #e8f4fd; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
    </style>
</head>
<body>
    <h1>短剧分类系统测试</h1>
    
    <div class="stats">
        <h2>统计信息</h2>
        <p id="stats-content">加载中...</p>
    </div>

    <div id="categories-container">
        <p>加载分类数据中...</p>
    </div>

    <script type="module">
        // 模拟导入配置数据
        const DRAMA_GENRE_GROUPS = {
            "爱情类": [
                "现代言情", "古代言情", "现言甜宠", "甜宠", "暗恋成真", "年代爱情", "闪婚",
                "破镜重圆", "青梅竹马", "强强联合", "姐弟恋", "一见钟情", "双向奔赴",
                "日久生情", "先婚后爱", "欢喜冤家", "相濡以沫", "奇幻爱情", "民国爱情",
                "仙侠情缘", "职场婚恋"
            ],
            "都市类": [
                "都市", "都市日常", "都市修仙", "都市情感", "都市脑洞", "职场", "娱乐圈",
                "校园", "家庭", "家庭伦理", "乡村", "青春", "医生", "明星"
            ],
            "古装类": [
                "古装", "王妃", "皇后", "古风权谋", "古风言情", "历史古代", "民国",
                "宫斗宅斗", "将军", "古灵精怪", "抗战谍战"
            ],
            "玄幻类": [
                "玄幻仙侠", "玄幻脑洞", "奇幻脑洞", "异能", "传承觉醒", "无敌神医",
                "战神归来", "系统", "龙王", "读心术", "武侠", "志怪", "天下无敌",
                "高手下山"
            ],
            "穿越重生类": [
                "穿越", "重生", "穿书", "快穿", "时空之旅", "灵魂互换"
            ],
            "悬疑类": [
                "悬疑", "悬疑推理", "特工", "法律正义", "法官"
            ],
            "其他类": [
                "喜剧", "科幻", "末世", "丧尸", "冒险", "求生", "种田经营", "反转", "剧情"
            ]
        };

        const DRAMA_THEMES = [
            "逆袭", "打脸虐渣", "女性成长", "小人物", "虐恋", "萌宝", "马甲", "病娇",
            "反派主角", "大叔", "团宠", "女帝", "豪门恩怨", "赘婿逆袭", "强者回归",
            "神豪", "真假千金", "亲情", "复仇", "灰姑娘", "替身", "大女主", "总裁",
            "霸总", "女强"
        ];

        // 渲染分类数据
        function renderCategories() {
            const container = document.getElementById('categories-container');
            const statsContent = document.getElementById('stats-content');
            
            // 计算统计信息
            const totalGenres = Object.values(DRAMA_GENRE_GROUPS).flat().length;
            const totalGroups = Object.keys(DRAMA_GENRE_GROUPS).length;
            const totalThemes = DRAMA_THEMES.length;
            
            statsContent.innerHTML = `
                <strong>总计:</strong> ${totalGroups} 个分组, ${totalGenres} 个类型, ${totalThemes} 个主题
            `;
            
            // 渲染分组
            let html = '<h2>短剧类型分组</h2>';
            for (const [groupName, genres] of Object.entries(DRAMA_GENRE_GROUPS)) {
                html += `
                    <div class="category-group">
                        <h3>${groupName} (${genres.length}个)</h3>
                        <div class="category-list">
                            ${genres.map(genre => `<span class="category-item">${genre}</span>`).join('')}
                        </div>
                    </div>
                `;
            }
            
            // 渲染主题
            html += `
                <div class="category-group">
                    <h3>常用主题 (${DRAMA_THEMES.length}个)</h3>
                    <div class="category-list">
                        ${DRAMA_THEMES.map(theme => `<span class="category-item">${theme}</span>`).join('')}
                    </div>
                </div>
            `;
            
            container.innerHTML = html;
        }

        // 页面加载完成后渲染
        document.addEventListener('DOMContentLoaded', renderCategories);
    </script>
</body>
</html>
