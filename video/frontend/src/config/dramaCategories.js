/**
 * 短剧分类配置
 * 从 duanju_categories.json 提取的分类数据
 */

// 短剧类型分类
export const DRAMA_GENRES = [
  "大女主", "总裁", "逆袭", "打脸虐渣", "重生", "女性成长", "奇幻爱情", "现代言情",
  "奇幻脑洞", "异能", "传承觉醒", "无敌神医", "战神归来", "都市修仙", "现言甜宠",
  "暗恋成真", "年代爱情", "闪婚", "马甲", "萌宝", "小人物", "虐恋", "古装",
  "王妃", "古风权谋", "古风言情", "皇后", "赘婿逆袭", "历史古代", "民国爱情",
  "抗战谍战", "强者回归", "都市日常", "乡村", "校园", "家庭伦理", "神豪",
  "玄幻仙侠", "法律正义", "喜剧", "职场", "穿越", "仙侠情缘", "穿书", "志怪",
  "娱乐圈", "系统", "真假千金", "亲情", "欢喜冤家", "病娇", "反派主角",
  "宫斗宅斗", "大叔", "团宠", "女帝", "豪门恩怨", "相濡以沫", "武侠", "法官",
  "高手下山", "破镜重圆", "青梅竹马", "强强联合", "天下无敌", "悬疑推理",
  "民国", "都市脑洞", "姐弟恋", "科幻", "替身", "灵魂互换", "时空之旅",
  "末世", "都市情感", "甜宠", "龙王", "读心术", "复仇", "剧情", "家庭",
  "青春", "灰姑娘", "冒险", "丧尸", "先婚后爱", "种田经营", "玄幻脑洞",
  "霸总", "职场婚恋", "反转", "古代言情", "快穿", "都市", "日久生情",
  "求生", "古灵精怪", "女强", "将军", "一见钟情", "医生", "悬疑", "特工",
  "明星", "双向奔赴"
]

// 按类别分组的分类
export const DRAMA_GENRE_GROUPS = {
  "爱情类": [
    "现代言情", "古代言情", "现言甜宠", "甜宠", "暗恋成真", "年代爱情", "闪婚",
    "破镜重圆", "青梅竹马", "强强联合", "姐弟恋", "一见钟情", "双向奔赴",
    "日久生情", "先婚后爱", "欢喜冤家", "相濡以沫", "奇幻爱情", "民国爱情",
    "仙侠情缘", "职场婚恋"
  ],
  "都市类": [
    "都市", "都市日常", "都市修仙", "都市情感", "都市脑洞", "职场", "娱乐圈",
    "校园", "家庭", "家庭伦理", "乡村", "青春", "医生", "明星"
  ],
  "古装类": [
    "古装", "王妃", "皇后", "古风权谋", "古风言情", "历史古代", "民国",
    "宫斗宅斗", "将军", "古灵精怪", "抗战谍战"
  ],
  "玄幻类": [
    "玄幻仙侠", "玄幻脑洞", "奇幻脑洞", "异能", "传承觉醒", "无敌神医",
    "战神归来", "系统", "龙王", "读心术", "武侠", "志怪", "天下无敌",
    "高手下山"
  ],
  "穿越重生类": [
    "穿越", "重生", "穿书", "快穿", "时空之旅", "灵魂互换"
  ],
  "悬疑类": [
    "悬疑", "悬疑推理", "特工", "法律正义", "法官"
  ],
  "其他类": [
    "喜剧", "科幻", "末世", "丧尸", "冒险", "求生", "种田经营", "反转", "剧情"
  ]
}

// 常用主题标签
export const DRAMA_THEMES = [
  "逆袭", "打脸虐渣", "女性成长", "小人物", "虐恋", "萌宝", "马甲", "病娇",
  "反派主角", "大叔", "团宠", "女帝", "豪门恩怨", "赘婿逆袭", "强者回归",
  "神豪", "真假千金", "亲情", "复仇", "灰姑娘", "替身", "大女主", "总裁",
  "霸总", "女强"
]

/**
 * 获取所有短剧类型
 */
export function getDramaGenres() {
  return DRAMA_GENRES
}

/**
 * 获取分组的短剧类型
 */
export function getDramaGenreGroups() {
  return DRAMA_GENRE_GROUPS
}

/**
 * 获取常用短剧主题
 */
export function getDramaThemes() {
  return DRAMA_THEMES
}

/**
 * 验证类型是否有效
 */
export function isValidGenre(genre) {
  return DRAMA_GENRES.includes(genre)
}
