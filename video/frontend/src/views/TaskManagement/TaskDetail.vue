<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 初始加载状态 - 骨架屏 -->
    <div v-if="initialLoading" class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <!-- 返回按钮骨架 -->
      <div class="mb-6">
        <div class="h-4 bg-gray-200 rounded w-24 animate-pulse"></div>
      </div>

      <!-- 任务头部信息骨架 -->
      <div class="bg-white rounded-lg shadow mb-6">
        <div class="p-6">
          <div class="flex justify-between items-start">
            <div class="flex-1">
              <div class="h-8 bg-gray-200 rounded w-64 mb-2 animate-pulse"></div>
              <div class="h-4 bg-gray-200 rounded w-96 mb-4 animate-pulse"></div>
              <div class="flex items-center space-x-6">
                <div class="h-4 bg-gray-200 rounded w-32 animate-pulse"></div>
                <div class="h-4 bg-gray-200 rounded w-20 animate-pulse"></div>
                <div class="h-6 bg-gray-200 rounded w-16 animate-pulse"></div>
              </div>
            </div>
            <div class="flex space-x-3">
              <div class="h-10 bg-gray-200 rounded w-24 animate-pulse"></div>
              <div class="h-10 bg-gray-200 rounded w-20 animate-pulse"></div>
            </div>
          </div>

          <!-- 进度条骨架 -->
          <div class="mt-6">
            <div class="flex justify-between mb-2">
              <div class="h-4 bg-gray-200 rounded w-16 animate-pulse"></div>
              <div class="h-4 bg-gray-200 rounded w-12 animate-pulse"></div>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-3 animate-pulse"></div>
          </div>
        </div>
      </div>

      <!-- 视频列表骨架 -->
      <div class="bg-white rounded-lg shadow mb-6">
        <div class="p-6">
          <div class="flex justify-between items-center mb-4">
            <div class="h-6 bg-gray-200 rounded w-20 animate-pulse"></div>
            <div class="h-10 bg-gray-200 rounded w-20 animate-pulse"></div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div v-for="i in 3" :key="i" class="border border-gray-200 rounded-lg p-4">
              <div class="aspect-video bg-gray-200 rounded mb-3 animate-pulse"></div>
              <div class="h-4 bg-gray-200 rounded w-full mb-2 animate-pulse"></div>
              <div class="h-3 bg-gray-200 rounded w-16 mb-2 animate-pulse"></div>
              <div class="h-2 bg-gray-200 rounded w-full animate-pulse"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 处理日志骨架 -->
      <div class="bg-white rounded-lg shadow">
        <div class="p-6">
          <div class="h-6 bg-gray-200 rounded w-20 mb-4 animate-pulse"></div>
          <div class="space-y-3">
            <div v-for="i in 4" :key="i" class="flex items-start space-x-3 p-3 bg-gray-50 rounded">
              <div class="w-2 h-2 bg-gray-200 rounded-full mt-2 animate-pulse"></div>
              <div class="flex-1">
                <div class="h-4 bg-gray-200 rounded w-full mb-1 animate-pulse"></div>
                <div class="h-3 bg-gray-200 rounded w-32 animate-pulse"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 任务详情 -->
    <transition name="fade" enter-active-class="transition-all duration-500 ease-out"
      enter-from-class="opacity-0 transform translate-y-2" enter-to-class="opacity-100 transform translate-y-0">
      <div v-if="task && !initialLoading" class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- 刷新指示器 -->
        <div v-if="refreshing" class="fixed top-4 right-4 z-50">
          <div class="bg-white rounded-lg shadow-lg p-3 flex items-center space-x-2">
            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-600"></div>
            <span class="text-sm text-gray-600">更新中...</span>
          </div>
        </div>
        <!-- 返回按钮 -->
        <div class="mb-6">
          <button @click="goBack" class="inline-flex items-center text-sm text-gray-500 hover:text-gray-700">
            <svg class="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
            返回任务列表
          </button>
        </div>

        <!-- 任务头部信息 -->
        <div class="bg-white rounded-lg shadow mb-6">
          <div class="p-6">
            <div class="flex justify-between items-start">
              <div class="flex-1">
                <h1 class="text-3xl font-bold text-gray-900">{{ task.name }}</h1>
                <p class="mt-2 text-gray-600">{{ task.description || '暂无描述' }}</p>

                <!-- 短剧元数据信息 -->
                <div v-if="task.synopsis || task.genre || task.theme" class="mt-4 bg-gray-50 rounded-lg p-4">
                  <h3 class="text-sm font-medium text-gray-900 mb-3">短剧信息</h3>
                  <div class="space-y-2">
                    <div v-if="task.synopsis" class="text-sm">
                      <span class="text-gray-500">简介：</span>
                      <span class="text-gray-900">{{ task.synopsis }}</span>
                    </div>
                    <div class="space-y-2">
                      <div v-if="task.genre && task.genre.length > 0" class="text-sm">
                        <span class="text-gray-500">类型：</span>
                        <div class="inline-flex flex-wrap gap-1 mt-1">
                          <span v-for="genre in task.genre" :key="genre"
                            class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {{ genre }}
                          </span>
                        </div>
                      </div>
                      <div v-if="task.theme && task.theme.length > 0" class="text-sm">
                        <span class="text-gray-500">主题：</span>
                        <div class="inline-flex flex-wrap gap-1 mt-1">
                          <span v-for="theme in task.theme" :key="theme"
                            class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            {{ theme }}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="mt-4 flex items-center space-x-6 text-sm text-gray-500">
                  <span>创建时间: {{ formatDate(task.created_at) }}</span>
                  <span>视频数量: {{ task.videos?.length || 0 }}</span>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                    :class="getStatusClass(task.status)">
                    {{ getStatusText(task.status) }}
                  </span>
                </div>
              </div>
              <div class="flex space-x-3">
                <button @click="showEditModal = true"
                  class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center space-x-2">
                  <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                  <span>编辑信息</span>
                </button>
                <button @click="refreshTask" :disabled="refreshing"
                  class="bg-gray-600 hover:bg-gray-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center space-x-2">
                  <svg class="h-4 w-4" :class="{ 'animate-spin': refreshing }" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  <span>{{ refreshing ? '刷新中...' : '刷新' }}</span>
                </button>
                <button v-if="task.status === 'pending' || task.status === 'paused'" @click="startTask"
                  class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                  {{ task.status === 'paused' ? '继续处理' : '开始处理' }}
                </button>
                <button v-if="task.status === 'processing'" @click="pauseTask"
                  class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                  暂停处理
                </button>
                <!-- 重试按钮 - 支持所有状态 -->
                <button @click="retryTask" :disabled="retrying"
                  class="bg-orange-600 hover:bg-orange-700 disabled:bg-orange-400 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center space-x-2">
                  <svg class="h-4 w-4" :class="{ 'animate-spin': retrying }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  <span>{{ retrying ? '重试中...' : '重试任务' }}</span>
                </button>
                <button @click="deleteTask"
                  class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                  删除任务
                </button>
              </div>
            </div>

            <!-- 总体进度 -->
            <div class="mt-6">
              <div class="flex justify-between text-sm text-gray-500 mb-2">
                <span>总体进度</span>
                <span>{{ Math.round(task.progress || 0) }}%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-3">
                <div class="bg-primary-600 h-3 rounded-full transition-all"
                  :style="{ width: `${task.progress || 0}%` }"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 视频列表 -->
        <div class="bg-white rounded-lg shadow mb-6">
          <div class="p-6">
            <div class="flex justify-between items-center mb-4">
              <h2 class="text-xl font-semibold text-gray-900">视频列表</h2>
              <button @click="showUploadModal = true"
                class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                添加视频
              </button>
            </div>

            <div v-if="task.videos && task.videos.length > 0"
              class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div v-for="video in task.videos" :key="video.id"
                class="border border-gray-200 rounded-lg p-4 hover:border-gray-300 cursor-pointer"
                @click="viewVideo(video.id)">
                <!-- 视频缩略图 -->
                <div class="aspect-video bg-gray-200 rounded mb-3 relative">
                  <img v-if="getVideoThumbnail(video)" :src="getVideoThumbnail(video)" :alt="video.original_filename"
                    class="w-full h-full object-cover rounded" @error="handleImageError" />
                  <div v-else class="w-full h-full flex items-center justify-center">
                    <svg class="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                  </div>

                  <!-- 处理状态指示器 -->
                  <div class="absolute top-2 right-2 w-3 h-3 rounded-full" :class="getVideoStatusColor(video.status)">
                  </div>
                </div>

                <!-- 视频信息 -->
                <div>
                  <h3 class="text-sm font-medium text-gray-900 truncate" :title="video.original_filename">
                    {{ video.original_filename }}
                  </h3>
                  <div class="text-xs text-gray-500 mt-1 space-y-1">
                    <div>{{ formatDuration(video.duration) }}</div>
                    <div v-if="video.resolution">{{ video.resolution }}</div>
                    <div v-if="video.file_size">{{ formatFileSize(video.file_size) }}</div>
                  </div>

                  <!-- 分析进度 -->
                  <div class="mt-2">
                    <div class="flex justify-between text-xs text-gray-500 mb-1">
                      <span>分析进度</span>
                      <span>{{ Math.round(video.analysis_progress || 0) }}%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-1">
                      <div class="bg-blue-600 h-1 rounded-full transition-all"
                        :style="{ width: `${video.analysis_progress || 0}%` }"></div>
                    </div>
                  </div>

                  <!-- 分析结果摘要 -->
                  <div v-if="video.analysis_summary" class="mt-2 text-xs text-gray-600">
                    <div class="flex justify-between">
                      <span>场景数:</span>
                      <span>{{ video.analysis_summary.scene_count || 0 }}</span>
                    </div>
                    <div class="flex justify-between">
                      <span>人物数:</span>
                      <span>{{ video.analysis_summary.character_count || 0 }}</span>
                    </div>
                    <div class="flex justify-between">
                      <span>对象数:</span>
                      <span>{{ video.analysis_summary.clip_count || 0 }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 空状态 -->
            <div v-else class="text-center py-12">
              <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
              </svg>
              <h3 class="mt-2 text-sm font-medium text-gray-900">暂无视频</h3>
              <p class="mt-1 text-sm text-gray-500">添加视频文件开始分析</p>
            </div>
          </div>
        </div>

        <!-- 处理日志 -->
        <div class="bg-white rounded-lg shadow">
          <div class="p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">处理日志</h2>
            <div v-if="logs && logs.length > 0" class="space-y-3 max-h-96 overflow-y-auto">
              <div v-for="log in logs" :key="log.id" class="flex items-start space-x-3 p-3 bg-gray-50 rounded">
                <div class="w-2 h-2 rounded-full mt-2 flex-shrink-0" :class="getLogLevelColor(log.level)"></div>
                <div class="flex-1 min-w-0">
                  <p class="text-sm text-gray-900">{{ log.message }}</p>
                  <p class="text-xs text-gray-500">{{ formatDate(log.timestamp) }}</p>
                </div>
              </div>
            </div>
            <div v-else class="text-center py-8">
              <svg class="mx-auto h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <p class="mt-2 text-sm text-gray-500">暂无处理日志</p>
            </div>
          </div>
        </div>
      </div>
    </transition>
    <!-- 错误状态 -->
    <div v-if="!initialLoading && !task" class="flex items-center justify-center min-h-screen">
      <div class="text-center">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">任务未找到</h3>
        <p class="mt-1 text-sm text-gray-500">请检查任务ID是否正确</p>
        <div class="mt-6">
          <button @click="goBack"
            class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium">
            返回列表
          </button>
        </div>
      </div>
    </div>

    <!-- 上传视频模态框 -->
    <div v-if="showUploadModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
      @click="showUploadModal = false">
      <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white" @click.stop>
        <div class="mt-3">
          <h3 class="text-lg font-medium text-gray-900 mb-4">添加视频</h3>

          <label class="block cursor-pointer">
            <input type="file" class="sr-only" multiple accept="video/*" @change="handleFileUpload" />
            <div
              class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
              <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                <path
                  d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                  stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
              </svg>
              <div class="mt-4">
                <span class="mt-2 block text-sm font-medium text-gray-900">
                  点击上传或拖拽文件到此处
                </span>
                <p class="mt-1 text-xs text-gray-500">
                  支持 MP4, MOV, AVI 等格式，单个文件最大 2GB
                </p>
              </div>
            </div>
          </label>

          <div class="flex justify-end space-x-3 mt-6">
            <button @click="showUploadModal = false"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md">
              取消
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑任务信息模态框 -->
    <div v-if="showEditModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
      @click="showEditModal = false">
      <div class="relative top-20 mx-auto p-5 border w-[500px] shadow-lg rounded-md bg-white" @click.stop>
        <div class="mt-3">
          <h3 class="text-lg font-medium text-gray-900 mb-4">编辑任务信息</h3>

          <form @submit.prevent="updateTaskInfo">
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">任务名称</label>
              <input v-model="editForm.name" type="text" required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                placeholder="请输入任务名称" />
            </div>

            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">任务描述</label>
              <textarea v-model="editForm.description" rows="3"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                placeholder="请输入任务描述（可选）"></textarea>
            </div>

            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">短剧简介</label>
              <textarea v-model="editForm.synopsis" rows="3"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                placeholder="请输入短剧简介（可选）"></textarea>
            </div>

            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">短剧类型（可多选）</label>
              <div class="border border-gray-300 rounded-md p-3 max-h-40 overflow-y-auto">
                <div class="space-y-2">
                  <div v-for="(groupGenres, groupName) in dramaGenreGroups" :key="groupName">
                    <div class="text-xs font-medium text-gray-500 mb-1">{{ groupName }}</div>
                    <div class="grid grid-cols-2 gap-1">
                      <label v-for="genre in groupGenres" :key="genre" class="flex items-center text-sm">
                        <input type="checkbox" :value="genre" v-model="editForm.genre"
                          class="mr-2 h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
                        {{ genre }}
                      </label>
                    </div>
                  </div>
                </div>
              </div>
              <div v-if="editForm.genre.length > 0" class="mt-2 flex flex-wrap gap-1">
                <span v-for="genre in editForm.genre" :key="genre"
                  class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  {{ genre }}
                  <button type="button" @click="removeGenre(genre)" class="ml-1 text-blue-600 hover:text-blue-800">
                    ×
                  </button>
                </span>
              </div>
            </div>

            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">短剧主题（可多选或自定义）</label>
              <div class="mb-2">
                <input v-model="newTheme" @keyup.enter="addTheme" type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  placeholder="输入主题关键词后按回车添加" />
              </div>
              <div class="border border-gray-300 rounded-md p-3 max-h-32 overflow-y-auto">
                <div class="grid grid-cols-3 gap-1">
                  <label v-for="theme in dramaThemes" :key="theme" class="flex items-center text-sm">
                    <input type="checkbox" :value="theme" v-model="editForm.theme"
                      class="mr-2 h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
                    {{ theme }}
                  </label>
                </div>
              </div>
              <div v-if="editForm.theme.length > 0" class="mt-2 flex flex-wrap gap-1">
                <span v-for="theme in editForm.theme" :key="theme"
                  class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  {{ theme }}
                  <button type="button" @click="removeTheme(theme)" class="ml-1 text-green-600 hover:text-green-800">
                    ×
                  </button>
                </span>
              </div>
            </div>

            <div class="flex justify-end space-x-3">
              <button type="button" @click="showEditModal = false"
                class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md">
                取消
              </button>
              <button type="submit" :disabled="!editForm.name.trim() || updating"
                class="px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50">
                {{ updating ? '更新中...' : '更新' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAppStore } from '@/stores/app'
import { useTaskStore } from '@/stores/tasks'
import { useVideoStore } from '@/stores/videos'
import { format } from 'date-fns'
import { getVideoThumbnail } from '@/utils/filePathUtils'
import { getDramaGenres, getDramaThemes, getDramaGenreGroups } from '@/config/dramaCategories'

const route = useRoute()
const router = useRouter()
const appStore = useAppStore()
const taskStore = useTaskStore()
const videoStore = useVideoStore()

// 响应式数据
const initialLoading = ref(true)
const refreshing = ref(false)
const retrying = ref(false)
const task = ref(null)
const logs = ref([])
const showUploadModal = ref(false)
const showEditModal = ref(false)
const updating = ref(false)
const newTheme = ref('')
const editForm = ref({
  name: '',
  description: '',
  synopsis: '',
  genre: [],  // 改为数组
  theme: []   // 改为数组
})

// 分类数据
const dramaGenres = ref(getDramaGenres())
const dramaGenreGroups = ref(getDramaGenreGroups())
const dramaThemes = ref(getDramaThemes())

// 方法
const loadTask = async (isRefresh = false) => {
  const startTime = Date.now()

  try {
    if (isRefresh) {
      refreshing.value = true
    }

    const taskId = route.params.id

    // 并行加载任务数据和日志数据
    const [taskData, logsData] = await Promise.all([
      taskStore.fetchTask(taskId),
      taskStore.fetchTaskLogs(taskId).catch(() => []) // 如果日志加载失败，使用空数组
    ])

    if (!isRefresh) {
      // 初始加载：确保骨架屏显示足够时间，避免闪动
      task.value = taskData
      logs.value = logsData
      populateEditForm(taskData)

      // 计算已经过去的时间
      const elapsed = Date.now() - startTime
      const minDisplayTime = 400 // 最少显示400ms

      if (elapsed < minDisplayTime) {
        // 如果加载太快，等待剩余时间
        await new Promise(resolve => setTimeout(resolve, minDisplayTime - elapsed))
      }

      initialLoading.value = false
    } else {
      // 刷新时直接更新
      task.value = taskData
      logs.value = logsData
      populateEditForm(taskData)
    }
  } catch (error) {
    if (!isRefresh) {
      appStore.showError('加载失败', '无法加载任务详情')
    }
    initialLoading.value = false
  } finally {
    if (isRefresh) {
      refreshing.value = false
    }
  }
}

const goBack = () => {
  router.push('/tasks')
}

// 手动刷新任务数据
const refreshTask = async () => {
  await loadTask(true)
}

// 填充编辑表单
const populateEditForm = (taskData) => {
  editForm.value = {
    name: taskData.name || '',
    description: taskData.description || '',
    synopsis: taskData.synopsis || '',
    genre: Array.isArray(taskData.genre) ? taskData.genre : (taskData.genre ? [taskData.genre] : []),
    theme: Array.isArray(taskData.theme) ? taskData.theme : (taskData.theme ? [taskData.theme] : [])
  }
}

// 更新任务信息
const updateTaskInfo = async () => {
  updating.value = true
  try {
    const updatedTask = await taskStore.updateTask(task.value.id, editForm.value)
    task.value = { ...task.value, ...updatedTask }
    showEditModal.value = false
    appStore.showSuccess('更新成功', '任务信息已更新')
  } catch (error) {
    appStore.showError('更新失败', '无法更新任务信息')
  } finally {
    updating.value = false
  }
}


const retryTask = async () => {
  try {
    retrying.value = true

    // 使用新的任务重试API
    await taskStore.retryTask(task.value.id)

    appStore.showSuccess('重试成功', '任务已重新启动')

    // 重新加载任务数据
    await loadTask(true)
  } catch (error) {
    appStore.showError('重试失败', error.response?.data?.detail || '无法重新启动任务')
  } finally {
    retrying.value = false
  }
}

const startTask = async () => {
  try {
    const response = await taskStore.startTaskProcessing(task.value.id)
    task.value.status = response.status
    task.value.progress = response.progress
    appStore.showSuccess('任务启动', '任务处理已开始')
  } catch (error) {
    if (error.response?.status === 400) {
      appStore.showError('启动失败', error.response.data.detail || '无法启动任务处理')
    } else {
      appStore.showError('启动失败', '无法启动任务处理')
    }
  }
}

const pauseTask = async () => {
  try {
    const response = await taskStore.pauseTaskProcessing(task.value.id)
    task.value.status = response.status
    appStore.showSuccess('任务暂停', '任务处理已暂停')
  } catch (error) {
    appStore.showError('暂停失败', '无法暂停任务处理')
  }
}

const deleteTask = async () => {
  if (confirm('确定要删除这个任务吗？此操作不可恢复。')) {
    try {
      await taskStore.deleteTask(task.value.id)
      appStore.showSuccess('删除成功', '任务已删除')
      router.push('/tasks')
    } catch (error) {
      appStore.showError('删除失败', '无法删除任务')
    }
  }
}

const viewVideo = (videoId) => {
  router.push(`/analysis/${videoId}`)
}

const handleFileUpload = async (event) => {
  const files = Array.from(event.target.files)
  if (files.length > 0) {
    showUploadModal.value = false

    for (const file of files) {
      try {
        appStore.showInfo('上传中', `正在上传 ${file.name}...`)
        await taskStore.uploadVideo(task.value.id, file)
        appStore.showSuccess('上传成功', `${file.name} 上传完成`)
      } catch (error) {
        appStore.showError('上传失败', `${file.name} 上传失败`)
      }
    }

    // 重新加载任务数据以获取最新的视频列表
    await loadTask(true)
  }
}

// 多选相关方法
const removeGenre = (genre) => {
  const index = editForm.value.genre.indexOf(genre)
  if (index > -1) {
    editForm.value.genre.splice(index, 1)
  }
}

const removeTheme = (theme) => {
  const index = editForm.value.theme.indexOf(theme)
  if (index > -1) {
    editForm.value.theme.splice(index, 1)
  }
}

const addTheme = () => {
  const theme = newTheme.value.trim()
  if (theme && !editForm.value.theme.includes(theme)) {
    editForm.value.theme.push(theme)
    newTheme.value = ''
  }
}



const getStatusClass = (status) => {
  const classes = {
    pending: 'bg-yellow-100 text-yellow-800',
    processing: 'bg-blue-100 text-blue-800',
    completed: 'bg-green-100 text-green-800',
    failed: 'bg-red-100 text-red-800',
    paused: 'bg-orange-100 text-orange-800'
  }
  return classes[status] || 'bg-gray-100 text-gray-800'
}

const getStatusText = (status) => {
  const texts = {
    pending: '待处理',
    processing: '处理中',
    completed: '已完成',
    failed: '失败',
    paused: '已暂停'
  }
  return texts[status] || '未知'
}

const getVideoStatusColor = (status) => {
  const colors = {
    pending: 'bg-gray-400',
    processing: 'bg-blue-500',
    completed: 'bg-green-500',
    failed: 'bg-red-500'
  }
  return colors[status] || 'bg-gray-400'
}

const getLogLevelColor = (level) => {
  const colors = {
    info: 'bg-blue-500',
    success: 'bg-green-500',
    warning: 'bg-yellow-500',
    error: 'bg-red-500'
  }
  return colors[level] || 'bg-gray-500'
}

const formatDuration = (seconds) => {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

const formatDate = (dateString) => {
  return format(new Date(dateString), 'yyyy-MM-dd HH:mm')
}

const formatFileSize = (bytes) => {
  if (!bytes) return '未知大小'
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
}

const handleImageError = (event) => {
  // 图片加载失败时隐藏图片元素
  event.target.style.display = 'none'
}

// 生命周期
onMounted(async () => {
  await loadTask()
})
</script>
