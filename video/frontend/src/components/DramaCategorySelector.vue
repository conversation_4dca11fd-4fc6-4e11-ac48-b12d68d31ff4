<template>
  <div class="grid grid-cols-2 gap-4">
    <!-- 短剧类型选择 -->
    <div>
      <label class="block text-sm font-medium text-gray-700 mb-2">短剧类型</label>
      <select
        :value="genre"
        @input="$emit('update:genre', $event.target.value)"
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
      >
        <option value="">请选择类型（可选）</option>
        <optgroup v-for="(groupGenres, groupName) in genreGroups" :key="groupName" :label="groupName">
          <option v-for="genreOption in groupGenres" :key="genreOption" :value="genreOption">
            {{ genreOption }}
          </option>
        </optgroup>
        <!-- 未分组的类型 -->
        <option v-for="genreOption in ungroupedGenres" :key="genreOption" :value="genreOption">
          {{ genreOption }}
        </option>
      </select>
    </div>
    
    <!-- 短剧主题输入 -->
    <div>
      <label class="block text-sm font-medium text-gray-700 mb-2">短剧主题</label>
      <div class="relative">
        <input
          :value="theme"
          @input="$emit('update:theme', $event.target.value)"
          type="text"
          :list="themeListId"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
          placeholder="请输入主题或从建议中选择（可选）"
        />
        <datalist :id="themeListId">
          <option v-for="themeOption in themes" :key="themeOption" :value="themeOption">
            {{ themeOption }}
          </option>
        </datalist>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import categoryService from '@/services/categoryService'

// Props
const props = defineProps({
  genre: {
    type: String,
    default: ''
  },
  theme: {
    type: String,
    default: ''
  },
  themeListId: {
    type: String,
    default: 'theme-suggestions'
  }
})

// Emits
defineEmits(['update:genre', 'update:theme'])

// 响应式数据
const genres = ref([])
const genreGroups = ref({})
const themes = ref([])

// 计算属性：获取未分组的类型
const ungroupedGenres = computed(() => {
  const groupedGenres = Object.values(genreGroups.value).flat()
  return genres.value.filter(genre => !groupedGenres.includes(genre))
})

// 加载分类数据
const loadCategories = async () => {
  try {
    genres.value = await categoryService.getGenres()
    genreGroups.value = await categoryService.getGenreGroups()
    themes.value = await categoryService.getThemes()
  } catch (error) {
    console.error('Failed to load categories:', error)
  }
}

onMounted(() => {
  loadCategories()
})
</script>
