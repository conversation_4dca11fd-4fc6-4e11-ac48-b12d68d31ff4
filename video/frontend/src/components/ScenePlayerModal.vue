<template>
  <div v-if="show" class="fixed inset-0 z-50 overflow-y-auto">
    <!-- 弹窗内容 -->
    <div class="flex min-h-full items-center justify-center p-4">
      <div class="relative bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        <!-- 头部 -->
        <div class="flex items-center justify-between p-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">
            {{ scene ? `场景 ${scene.id} 播放` : '场景播放' }}
          </h3>
          <button
            @click="closeModal"
            class="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <!-- 视频播放区域 -->
        <div class="p-4">
          <div class="aspect-video bg-black rounded-lg overflow-hidden relative">
            <video
              v-if="videoUrl"
              ref="videoPlayer"
              class="w-full h-full object-contain"
              controls
              preload="metadata"
              @loadedmetadata="onVideoLoaded"
              @error="onVideoError"
              :key="videoUrl"
            >
              <source :src="videoUrl" type="video/mp4">
              <p class="text-white text-center">您的浏览器不支持视频播放</p>
            </video>
            
            <!-- 加载状态 -->
            <div v-if="loading" class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
              <div class="text-white text-center">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
                <p v-if="splitting">正在分割场景...</p>
                <p v-else>加载中...</p>
              </div>
            </div>
            
            <!-- 错误状态 -->
            <div v-if="error" class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
              <div class="text-white text-center">
                <svg class="w-12 h-12 mx-auto mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p>视频加载失败</p>
                <button
                  @click="retryLoad"
                  class="mt-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm"
                >
                  重试
                </button>
              </div>
            </div>
          </div>

          <!-- 场景信息 -->
          <div v-if="scene" class="mt-4 bg-gray-50 rounded-lg p-4">
            <h4 class="text-sm font-medium text-gray-900 mb-2">场景信息</h4>
            <div class="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span class="text-gray-500">场景编号：</span>
                <span class="font-medium">{{ scene.id }}</span>
              </div>
              <div>
                <span class="text-gray-500">时长：</span>
                <span class="font-medium">{{ formatDuration(scene.duration) }}</span>
              </div>
              <div>
                <span class="text-gray-500">开始时间：</span>
                <span class="font-medium">{{ formatTime(scene.start_time) }}</span>
              </div>
              <div>
                <span class="text-gray-500">结束时间：</span>
                <span class="font-medium">{{ formatTime(scene.start_time + scene.duration) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 底部操作按钮 -->
        <div class="flex items-center justify-end space-x-3 p-4 border-t border-gray-200">
          <button
            @click="splitScenes"
            :disabled="splitting"
            class="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded text-sm transition-colors"
          >
            {{ splitting ? '分割中...' : '分割所有场景' }}
          </button>
          <button
            @click="closeModal"
            class="px-4 py-2 bg-gray-300 hover:bg-gray-400 text-gray-700 rounded text-sm transition-colors"
          >
            关闭
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { useVideoStore } from '@/stores/videos'
import { useAppStore } from '@/stores/app'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  scene: {
    type: Object,
    default: null
  },
  videoId: {
    type: Number,
    required: true
  }
})

const emit = defineEmits(['close', 'scenes-split'])

const videoStore = useVideoStore()
const appStore = useAppStore()

// 响应式数据
const loading = ref(false)
const error = ref(false)
const splitting = ref(false)
const videoPlayer = ref(null)
const sceneReady = ref(false)

// 计算属性
const videoUrl = computed(() => {
  if (!props.scene || !props.videoId || isNaN(props.videoId) || !sceneReady.value) return null
  return `/api/v1/videos/${props.videoId}/scenes/${props.scene.id}/file`
})

// 方法
const closeModal = () => {
  // 重置状态
  sceneReady.value = false
  loading.value = false
  error.value = false
  splitting.value = false
  emit('close')
}

const onVideoLoaded = () => {
  loading.value = false
  error.value = false
}

const onVideoError = () => {
  loading.value = false
  error.value = true
}

const retryLoad = () => {
  error.value = false
  loading.value = true
  if (videoPlayer.value) {
    videoPlayer.value.load()
  }
}

const checkSceneFile = async () => {
  if (!props.scene || !props.videoId || isNaN(props.videoId)) {
    console.error('Invalid scene or videoId:', { scene: props.scene, videoId: props.videoId })
    return false
  }

  try {
    const response = await fetch(`/api/v1/videos/${props.videoId}/scenes/${props.scene.id}/file`, {
      method: 'HEAD'
    })
    return response.ok
  } catch (error) {
    console.error('Error checking scene file:', error)
    return false
  }
}

const ensureSceneReady = async () => {
  loading.value = true
  error.value = false

  try {
    // 先检查场景文件是否存在
    const exists = await checkSceneFile()

    if (!exists) {
      // 如果不存在，先分割场景
      console.log('Scene file not found, splitting scenes...')
      await splitScenes()
    }

    sceneReady.value = true
    loading.value = false
  } catch (error) {
    console.error('Failed to ensure scene ready:', error)
    error.value = true
    loading.value = false
  }
}

const splitScenes = async () => {
  if (splitting.value) return

  if (!props.videoId || isNaN(props.videoId)) {
    console.error('Invalid videoId for scene splitting:', props.videoId)
    appStore.showError('分割失败', '无效的视频ID')
    return
  }

  splitting.value = true
  try {
    console.log('Splitting scenes for video:', props.videoId)
    const response = await fetch(`/api/v1/videos/${props.videoId}/scenes/split`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: 'output_format=mp4'
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.detail || '分割失败')
    }

    const result = await response.json()
    appStore.showSuccess('分割成功', `成功分割 ${result.data.total_scenes} 个场景`)
    emit('scenes-split', result.data)
  } catch (error) {
    console.error('Scene splitting failed:', error)
    appStore.showError('分割失败', error.message || '场景分割失败，请重试')
    throw error
  } finally {
    splitting.value = false
  }
}

// 工具函数
const formatDuration = (seconds) => {
  if (!seconds) return '0:00'
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

const formatTime = (seconds) => {
  if (!seconds) return '0:00'
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

// 监听器
watch(() => props.show, (newShow) => {
  if (newShow && props.scene) {
    console.log('ScenePlayerModal opened with:', {
      scene: props.scene,
      videoId: props.videoId,
      isValidVideoId: !isNaN(props.videoId)
    })
    sceneReady.value = false
    ensureSceneReady()
  }
})

watch(() => sceneReady.value, (ready) => {
  if (ready) {
    nextTick(() => {
      if (videoPlayer.value) {
        videoPlayer.value.load()
      }
    })
  }
})
</script>

<style scoped>
/* 自定义视频播放器样式 */
video {
  background: transparent !important;
}

/* 确保弹窗在最顶层 */
.fixed {
  z-index: 9999;
}
</style>
