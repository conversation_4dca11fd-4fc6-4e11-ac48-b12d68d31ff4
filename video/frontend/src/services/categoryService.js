import client from '@/api/client'
import { getDramaGenres, getDramaThemes, getDramaGenreGroups } from '@/config/dramaCategories'

/**
 * 分类服务
 * 提供短剧分类数据的获取和管理
 */
class CategoryService {
  constructor() {
    this.cachedCategories = null
  }

  /**
   * 从后端获取分类数据
   */
  async fetchCategoriesFromAPI() {
    try {
      const response = await client.get('/tasks/categories')
      return response
    } catch (error) {
      console.warn('Failed to fetch categories from API, using local config:', error)
      // 如果API失败，使用本地配置
      return {
        genres: getDramaGenres(),
        genre_groups: getDramaGenreGroups(),
        themes: getDramaThemes()
      }
    }
  }

  /**
   * 获取所有分类数据（带缓存）
   */
  async getCategories() {
    if (!this.cachedCategories) {
      this.cachedCategories = await this.fetchCategoriesFromAPI()
    }
    return this.cachedCategories
  }

  /**
   * 获取短剧类型列表
   */
  async getGenres() {
    const categories = await this.getCategories()
    return categories.genres || getDramaGenres()
  }

  /**
   * 获取分组的短剧类型
   */
  async getGenreGroups() {
    const categories = await this.getCategories()
    return categories.genre_groups || getDramaGenreGroups()
  }

  /**
   * 获取主题列表
   */
  async getThemes() {
    const categories = await this.getCategories()
    return categories.themes || getDramaThemes()
  }

  /**
   * 清除缓存（当配置更新时调用）
   */
  clearCache() {
    this.cachedCategories = null
  }

  /**
   * 验证类型是否有效
   */
  async isValidGenre(genre) {
    const genres = await this.getGenres()
    return genres.includes(genre)
  }
}

// 创建单例实例
const categoryService = new CategoryService()

export default categoryService
