import { defineStore } from 'pinia'
import { ref } from 'vue'
import client from '@/api/client'

export const useTaskStore = defineStore('tasks', () => {
  // 状态
  const tasks = ref([])
  const currentTask = ref(null)

  // 操作
  const fetchTasks = async () => {
    try {
      const response = await client.get('/tasks')
      tasks.value = response
      return response
    } catch (error) {
      console.error('Failed to fetch tasks:', error)
      throw error
    }
  }

  const fetchTask = async (taskId) => {
    try {
      const response = await client.get(`/tasks/${taskId}`)
      currentTask.value = response
      return response
    } catch (error) {
      console.error('Failed to fetch task:', error)
      throw error
    }
  }

  const createTask = async (taskData) => {
    try {
      const formData = new FormData()
      formData.append('name', taskData.name)
      formData.append('description', taskData.description || '')
      formData.append('synopsis', taskData.synopsis || '')
      formData.append('genre', JSON.stringify(taskData.genre || []))
      formData.append('theme', JSON.stringify(taskData.theme || []))
      formData.append('config', JSON.stringify(taskData.config || {}))

      const response = await client.post('/tasks', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })

      // 更新本地任务列表
      tasks.value.unshift(response)
      return response
    } catch (error) {
      console.error('Failed to create task:', error)
      throw error
    }
  }

  const updateTask = async (taskId, updates) => {
    try {
      const formData = new FormData()
      Object.keys(updates).forEach(key => {
        if (key === 'genre' || key === 'theme') {
          // 对于 genre 和 theme，确保是 JSON 字符串
          formData.append(key, JSON.stringify(updates[key] || []))
        } else {
          formData.append(key, updates[key])
        }
      })

      const response = await client.put(`/tasks/${taskId}`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })

      // 更新本地任务列表
      const index = tasks.value.findIndex(task => task.id === taskId)
      if (index !== -1) {
        tasks.value[index] = { ...tasks.value[index], ...response }
      }

      // 更新当前任务
      if (currentTask.value && currentTask.value.id === taskId) {
        currentTask.value = { ...currentTask.value, ...response }
      }

      return response
    } catch (error) {
      console.error('Failed to update task:', error)
      throw error
    }
  }

  const deleteTask = async (taskId) => {
    try {
      await client.delete(`/tasks/${taskId}`)

      // 从本地任务列表中移除
      tasks.value = tasks.value.filter(task => task.id !== taskId)

      // 清除当前任务（如果是被删除的任务）
      if (currentTask.value && currentTask.value.id === taskId) {
        currentTask.value = null
      }
    } catch (error) {
      console.error('Failed to delete task:', error)
      throw error
    }
  }

  // 重试任务
  const retryTask = async (taskId) => {
    try {
      const response = await client.post(`/tasks/${taskId}/retry`)

      // 更新本地任务状态
      const task = tasks.value.find(t => t.id === taskId)
      if (task) {
        task.status = 'pending'
        task.progress = 0.0
        task.error_message = null
      }

      if (currentTask.value && currentTask.value.id === taskId) {
        currentTask.value.status = 'pending'
        currentTask.value.progress = 0.0
        currentTask.value.error_message = null
      }

      return response
    } catch (error) {
      console.error('Failed to retry task:', error)
      throw error
    }
  }

  const uploadVideo = async (taskId, file) => {
    try {
      const formData = new FormData()
      formData.append('file', file)

      const response = await client.post(`/tasks/${taskId}/upload`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })

      return response
    } catch (error) {
      console.error('Failed to upload video:', error)
      throw error
    }
  }

  const startTaskProcessing = async (taskId) => {
    try {
      const response = await client.post(`/tasks/${taskId}/start`)

      // 更新本地任务状态
      const index = tasks.value.findIndex(task => task.id === taskId)
      if (index !== -1) {
        tasks.value[index] = { ...tasks.value[index], ...response }
      }

      // 更新当前任务
      if (currentTask.value && currentTask.value.id === taskId) {
        currentTask.value = { ...currentTask.value, ...response }
      }

      return response
    } catch (error) {
      console.error('Failed to start task processing:', error)
      throw error
    }
  }

  const pauseTaskProcessing = async (taskId) => {
    try {
      const response = await client.post(`/tasks/${taskId}/pause`)

      // 更新本地任务状态
      const index = tasks.value.findIndex(task => task.id === taskId)
      if (index !== -1) {
        tasks.value[index] = { ...tasks.value[index], ...response }
      }

      // 更新当前任务
      if (currentTask.value && currentTask.value.id === taskId) {
        currentTask.value = { ...currentTask.value, ...response }
      }

      return response
    } catch (error) {
      console.error('Failed to pause task processing:', error)
      throw error
    }
  }

  const fetchTaskLogs = async (taskId) => {
    try {
      const response = await client.get(`/tasks/${taskId}/logs`)
      return response
    } catch (error) {
      console.error('Failed to fetch task logs:', error)
      throw error
    }
  }

  return {
    // 状态
    tasks,
    currentTask,

    // 操作
    fetchTasks,
    fetchTask,
    createTask,
    updateTask,
    deleteTask,
    retryTask,
    uploadVideo,
    startTaskProcessing,
    pauseTaskProcessing,
    fetchTaskLogs
  }
})
