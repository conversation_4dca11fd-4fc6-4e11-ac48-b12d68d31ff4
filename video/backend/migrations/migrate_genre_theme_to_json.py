#!/usr/bin/env python3
"""
数据库迁移脚本：将 Task 表的 genre 和 theme 字段从字符串类型迁移到 JSON 类型
运行方式：python migrate_genre_theme_to_json.py
"""

import sys
import os
import json
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy import create_engine, text, MetaData, Table, Column, JSON, String, Text
from sqlalchemy.orm import sessionmaker
from app.core.config import settings
from app.core.database import Base
from app.models.task import Task

def migrate_genre_theme_to_json():
    """迁移 genre 和 theme 字段到 JSON 类型"""
    
    # 创建数据库连接
    engine = create_engine(settings.DATABASE_URL)
    Session = sessionmaker(bind=engine)
    session = Session()
    
    try:
        print("开始迁移 genre 和 theme 字段...")
        
        # 1. 备份现有数据
        print("1. 备份现有数据...")
        tasks = session.execute(text("SELECT id, genre, theme FROM tasks")).fetchall()
        backup_data = []
        
        for task in tasks:
            backup_data.append({
                'id': task.id,
                'genre': task.genre,
                'theme': task.theme
            })
        
        print(f"备份了 {len(backup_data)} 条任务数据")
        
        # 2. 创建临时列
        print("2. 创建临时 JSON 列...")
        try:
            session.execute(text("ALTER TABLE tasks ADD COLUMN genre_json JSON"))
            session.execute(text("ALTER TABLE tasks ADD COLUMN theme_json JSON"))
            session.commit()
        except Exception as e:
            if "already exists" not in str(e).lower():
                raise e
            print("临时列已存在，跳过创建")
        
        # 3. 迁移数据
        print("3. 迁移数据到 JSON 格式...")
        for task_data in backup_data:
            genre_list = []
            theme_list = []
            
            # 转换 genre
            if task_data['genre']:
                # 如果是逗号分隔的字符串，分割成列表
                if ',' in task_data['genre']:
                    genre_list = [g.strip() for g in task_data['genre'].split(',') if g.strip()]
                else:
                    genre_list = [task_data['genre'].strip()] if task_data['genre'].strip() else []
            
            # 转换 theme
            if task_data['theme']:
                # 如果是逗号分隔的字符串，分割成列表
                if ',' in task_data['theme']:
                    theme_list = [t.strip() for t in task_data['theme'].split(',') if t.strip()]
                else:
                    theme_list = [task_data['theme'].strip()] if task_data['theme'].strip() else []
            
            # 更新临时列
            session.execute(
                text("UPDATE tasks SET genre_json = :genre, theme_json = :theme WHERE id = :id"),
                {
                    'id': task_data['id'],
                    'genre': json.dumps(genre_list) if genre_list else None,
                    'theme': json.dumps(theme_list) if theme_list else None
                }
            )
        
        session.commit()
        print("数据迁移完成")
        
        # 4. 删除旧列，重命名新列
        print("4. 更新表结构...")
        session.execute(text("ALTER TABLE tasks DROP COLUMN genre"))
        session.execute(text("ALTER TABLE tasks DROP COLUMN theme"))
        session.execute(text("ALTER TABLE tasks RENAME COLUMN genre_json TO genre"))
        session.execute(text("ALTER TABLE tasks RENAME COLUMN theme_json TO theme"))
        session.commit()
        
        print("表结构更新完成")
        
        # 5. 验证迁移结果
        print("5. 验证迁移结果...")
        migrated_tasks = session.execute(text("SELECT id, genre, theme FROM tasks")).fetchall()
        
        for task in migrated_tasks:
            if task.genre:
                try:
                    genre_data = json.loads(task.genre) if isinstance(task.genre, str) else task.genre
                    print(f"任务 {task.id}: genre = {genre_data}")
                except:
                    print(f"任务 {task.id}: genre 解析失败 = {task.genre}")
            
            if task.theme:
                try:
                    theme_data = json.loads(task.theme) if isinstance(task.theme, str) else task.theme
                    print(f"任务 {task.id}: theme = {theme_data}")
                except:
                    print(f"任务 {task.id}: theme 解析失败 = {task.theme}")
        
        print("迁移完成！")
        
    except Exception as e:
        print(f"迁移失败: {e}")
        session.rollback()
        raise
    finally:
        session.close()

if __name__ == "__main__":
    migrate_genre_theme_to_json()
