"""
视觉模型统一接口
定义所有视觉理解模型的标准接口，支持多图理解能力
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Union
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


class VisionModelInterface(ABC):
    """视觉模型抽象接口"""
    
    def __init__(self, model_name: str, config: Dict[str, Any] = None):
        self.model_name = model_name
        self.config = config or {}
        self.is_initialized = False
    
    @abstractmethod
    async def initialize(self) -> bool:
        """初始化模型"""
        pass
    
    @abstractmethod
    async def analyze_single_image(
        self, 
        image_path: str, 
        question: str = "请详细描述这张图片的内容",
        **kwargs
    ) -> Dict[str, Any]:
        """分析单张图片"""
        pass
    
    @abstractmethod
    async def analyze_multiple_images(
        self, 
        image_paths: List[str], 
        question: str = "请分析这些图片的内容和关联性",
        **kwargs
    ) -> Dict[str, Any]:
        """分析多张图片（多图理解）"""
        pass
    
    @abstractmethod
    async def compare_images(
        self, 
        image_paths: List[str], 
        question: str = "请比较这些图片的异同点",
        **kwargs
    ) -> Dict[str, Any]:
        """比较多张图片"""
        pass
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            if not self.is_initialized:
                await self.initialize()
            
            return {
                "model_name": self.model_name,
                "status": "healthy",
                "initialized": self.is_initialized
            }
        except Exception as e:
            logger.error(f"Health check failed for {self.model_name}: {e}")
            return {
                "model_name": self.model_name,
                "status": "unhealthy",
                "error": str(e),
                "initialized": False
            }
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            "model_name": self.model_name,
            "config": self.config,
            "initialized": self.is_initialized,
            "capabilities": self.get_capabilities()
        }
    
    @abstractmethod
    def get_capabilities(self) -> List[str]:
        """获取模型能力列表"""
        pass
    
    def validate_image_path(self, image_path: str) -> bool:
        """验证图片路径"""
        try:
            path = Path(image_path)
            if not path.exists():
                logger.error(f"Image file not found: {image_path}")
                return False
            
            # 检查文件扩展名
            valid_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.gif', '.webp'}
            if path.suffix.lower() not in valid_extensions:
                logger.error(f"Unsupported image format: {path.suffix}")
                return False
            
            return True
        except Exception as e:
            logger.error(f"Error validating image path {image_path}: {e}")
            return False
    
    def validate_image_paths(self, image_paths: List[str]) -> List[str]:
        """验证多个图片路径，返回有效的路径列表"""
        valid_paths = []
        for path in image_paths:
            if self.validate_image_path(path):
                valid_paths.append(path)
            else:
                logger.warning(f"Skipping invalid image path: {path}")
        return valid_paths


class VisionModelError(Exception):
    """视觉模型异常"""
    
    def __init__(self, message: str, model_name: str = None, error_code: str = None):
        self.message = message
        self.model_name = model_name
        self.error_code = error_code
        super().__init__(self.message)


class VisionModelConfig:
    """视觉模型配置类"""
    
    def __init__(self, 
                 api_key: str = None,
                 base_url: str = None,
                 timeout: int = 30,
                 max_retries: int = 3,
                 max_images_per_request: int = 10,
                 supported_formats: List[str] = None):
        self.api_key = api_key
        self.base_url = base_url
        self.timeout = timeout
        self.max_retries = max_retries
        self.max_images_per_request = max_images_per_request
        self.supported_formats = supported_formats or ['.jpg', '.jpeg', '.png', '.bmp', '.gif', '.webp']
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "api_key": "***" if self.api_key else None,  # 隐藏敏感信息
            "base_url": self.base_url,
            "timeout": self.timeout,
            "max_retries": self.max_retries,
            "max_images_per_request": self.max_images_per_request,
            "supported_formats": self.supported_formats
        }


# 标准响应格式
class VisionAnalysisResult:
    """视觉分析结果标准格式"""
    
    def __init__(self,
                 model_name: str,
                 analysis_type: str,
                 content: str,
                 confidence: float = None,
                 metadata: Dict[str, Any] = None,
                 processing_time: float = None):
        self.model_name = model_name
        self.analysis_type = analysis_type  # single_image, multiple_images, compare_images
        self.content = content
        self.confidence = confidence
        self.metadata = metadata or {}
        self.processing_time = processing_time
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "model_name": self.model_name,
            "analysis_type": self.analysis_type,
            "content": self.content,
            "confidence": self.confidence,
            "metadata": self.metadata,
            "processing_time": self.processing_time
        }
