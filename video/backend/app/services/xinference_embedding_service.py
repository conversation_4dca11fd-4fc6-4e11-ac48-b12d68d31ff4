"""
Xinference Embedding 服务
提供文本向量化和相似度计算功能
"""

import numpy as np
from typing import Dict, List, Optional, Any, Union
from loguru import logger
import json

from app.core.config import settings
from app.core.xinference_config import ModelType, get_default_model_name
from app.services.xinference_client_service import xinference_client


class XinferenceEmbeddingService:
    """Xinference Embedding 服务"""
    
    def __init__(self):
        self.default_model = get_default_model_name(ModelType.EMBEDDING)
    
    async def create_embedding(
        self,
        input_text: Union[str, List[str]],
        model_name: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        创建文本向量
        
        Args:
            input_text: 输入文本或文本列表
            model_name: 模型名称，默认使用配置的默认模型
            **kwargs: 其他参数
            
        Returns:
            向量化结果
        """
        try:
            model_name = model_name or self.default_model
            
            # 确保模型可用
            model_uid = await xinference_client.ensure_model_available(
                model_name, ModelType.EMBEDDING
            )
            
            if not model_uid:
                raise RuntimeError(f"Failed to launch Embedding model: {model_name}")
            
            # 构建请求参数
            request_data = {
                "model": model_uid,
                "input": input_text,
                **kwargs
            }
            
            # 发送向量化请求
            response = await xinference_client._make_request(
                "POST",
                "/v1/embeddings",
                json=request_data
            )
            
            logger.info(f"Embedding creation completed for model {model_name}")
            return response
            
        except Exception as e:
            logger.error(f"Embedding creation failed: {e}")
            raise
    
    async def compute_similarity(
        self,
        text1: str,
        text2: str,
        model_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        计算两个文本的相似度
        
        Args:
            text1: 第一个文本
            text2: 第二个文本
            model_name: 模型名称
            
        Returns:
            相似度结果
        """
        try:
            # 获取两个文本的向量
            response = await self.create_embedding([text1, text2], model_name)
            
            embeddings = [item["embedding"] for item in response["data"]]
            
            if len(embeddings) != 2:
                raise ValueError("Expected 2 embeddings")
            
            # 计算余弦相似度
            vec1 = np.array(embeddings[0])
            vec2 = np.array(embeddings[1])
            
            # 余弦相似度
            cosine_similarity = np.dot(vec1, vec2) / (np.linalg.norm(vec1) * np.linalg.norm(vec2))
            
            # 欧几里得距离
            euclidean_distance = np.linalg.norm(vec1 - vec2)
            
            return {
                "text1": text1,
                "text2": text2,
                "cosine_similarity": float(cosine_similarity),
                "euclidean_distance": float(euclidean_distance),
                "model": model_name or self.default_model,
                "usage": response.get("usage", {})
            }
            
        except Exception as e:
            logger.error(f"Similarity computation failed: {e}")
            raise
    
    async def find_similar_texts(
        self,
        query_text: str,
        candidate_texts: List[str],
        top_k: int = 5,
        model_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        在候选文本中找到最相似的文本
        
        Args:
            query_text: 查询文本
            candidate_texts: 候选文本列表
            top_k: 返回最相似的前k个
            model_name: 模型名称
            
        Returns:
            相似度排序结果
        """
        try:
            # 获取所有文本的向量
            all_texts = [query_text] + candidate_texts
            response = await self.create_embedding(all_texts, model_name)
            
            embeddings = [item["embedding"] for item in response["data"]]
            query_embedding = np.array(embeddings[0])
            candidate_embeddings = [np.array(emb) for emb in embeddings[1:]]
            
            # 计算相似度
            similarities = []
            for i, candidate_emb in enumerate(candidate_embeddings):
                cosine_sim = np.dot(query_embedding, candidate_emb) / (
                    np.linalg.norm(query_embedding) * np.linalg.norm(candidate_emb)
                )
                similarities.append({
                    "index": i,
                    "text": candidate_texts[i],
                    "similarity": float(cosine_sim)
                })
            
            # 按相似度排序
            similarities.sort(key=lambda x: x["similarity"], reverse=True)
            
            return {
                "query_text": query_text,
                "results": similarities[:top_k],
                "total_candidates": len(candidate_texts),
                "model": model_name or self.default_model,
                "usage": response.get("usage", {})
            }
            
        except Exception as e:
            logger.error(f"Similar text search failed: {e}")
            raise
    
    async def cluster_texts(
        self,
        texts: List[str],
        num_clusters: int = 3,
        model_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        对文本进行聚类
        
        Args:
            texts: 文本列表
            num_clusters: 聚类数量
            model_name: 模型名称
            
        Returns:
            聚类结果
        """
        try:
            from sklearn.cluster import KMeans
            
            # 获取文本向量
            response = await self.create_embedding(texts, model_name)
            embeddings = np.array([item["embedding"] for item in response["data"]])
            
            # K-means 聚类
            kmeans = KMeans(n_clusters=num_clusters, random_state=42)
            cluster_labels = kmeans.fit_predict(embeddings)
            
            # 组织聚类结果
            clusters = {}
            for i, (text, label) in enumerate(zip(texts, cluster_labels)):
                label = int(label)
                if label not in clusters:
                    clusters[label] = []
                clusters[label].append({
                    "index": i,
                    "text": text
                })
            
            return {
                "texts": texts,
                "num_clusters": num_clusters,
                "clusters": clusters,
                "cluster_centers": kmeans.cluster_centers_.tolist(),
                "model": model_name or self.default_model,
                "usage": response.get("usage", {})
            }
            
        except ImportError:
            logger.error("scikit-learn is required for clustering")
            raise RuntimeError("scikit-learn is required for clustering")
        except Exception as e:
            logger.error(f"Text clustering failed: {e}")
            raise
    
    async def semantic_search(
        self,
        query: str,
        documents: List[Dict[str, Any]],
        text_field: str = "content",
        top_k: int = 10,
        model_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        语义搜索
        
        Args:
            query: 搜索查询
            documents: 文档列表，每个文档包含文本和元数据
            text_field: 文档中文本字段的名称
            top_k: 返回最相关的前k个文档
            model_name: 模型名称
            
        Returns:
            搜索结果
        """
        try:
            # 提取文档文本
            doc_texts = [doc.get(text_field, "") for doc in documents]
            
            # 查找相似文本
            similarity_result = await self.find_similar_texts(
                query, doc_texts, top_k, model_name
            )
            
            # 组合文档元数据
            search_results = []
            for result in similarity_result["results"]:
                doc_index = result["index"]
                search_results.append({
                    "document": documents[doc_index],
                    "similarity": result["similarity"],
                    "matched_text": result["text"]
                })
            
            return {
                "query": query,
                "results": search_results,
                "total_documents": len(documents),
                "model": model_name or self.default_model,
                "usage": similarity_result.get("usage", {})
            }
            
        except Exception as e:
            logger.error(f"Semantic search failed: {e}")
            raise
    
    async def get_text_embeddings_batch(
        self,
        texts: List[str],
        batch_size: int = 100,
        model_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        批量获取文本向量
        
        Args:
            texts: 文本列表
            batch_size: 批处理大小
            model_name: 模型名称
            
        Returns:
            批量向量化结果
        """
        try:
            all_embeddings = []
            total_usage = {"prompt_tokens": 0, "total_tokens": 0}
            
            # 分批处理
            for i in range(0, len(texts), batch_size):
                batch_texts = texts[i:i + batch_size]
                
                response = await self.create_embedding(batch_texts, model_name)
                
                batch_embeddings = [item["embedding"] for item in response["data"]]
                all_embeddings.extend(batch_embeddings)
                
                # 累计使用量
                usage = response.get("usage", {})
                total_usage["prompt_tokens"] += usage.get("prompt_tokens", 0)
                total_usage["total_tokens"] += usage.get("total_tokens", 0)
                
                logger.info(f"Processed batch {i//batch_size + 1}/{(len(texts) + batch_size - 1)//batch_size}")
            
            return {
                "texts": texts,
                "embeddings": all_embeddings,
                "batch_size": batch_size,
                "total_batches": (len(texts) + batch_size - 1) // batch_size,
                "model": model_name or self.default_model,
                "usage": total_usage
            }
            
        except Exception as e:
            logger.error(f"Batch embedding failed: {e}")
            raise


# 全局 Embedding 服务实例
xinference_embedding_service = XinferenceEmbeddingService()
