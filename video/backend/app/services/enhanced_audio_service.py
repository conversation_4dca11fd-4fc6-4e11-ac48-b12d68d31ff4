"""
增强的音频处理服务
集成 Xinference 和本地音频处理能力
"""

import os
from typing import Dict, List, Optional, Any, Union
from loguru import logger
from sqlalchemy.orm import Session

from app.core.config import settings
from app.services.local_audio_service import local_audio_service
from app.services.xinference_service import xinference_service
from app.models.task import Video, AudioTrack, Subtitle
from app.utils.file_organization import VideoFileOrganizer


class EnhancedAudioService:
    """增强的音频处理服务"""
    
    def __init__(self, db: Session):
        self.db = db
        self.file_organizer = VideoFileOrganizer()
    
    async def transcribe_video_audio(
        self,
        video_id: int,
        use_xinference: Optional[bool] = None,
        model_name: Optional[str] = None,
        language: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        转录视频音频
        
        Args:
            video_id: 视频ID
            use_xinference: 是否使用 Xinference，None 时自动选择
            model_name: 模型名称
            language: 语言代码
            
        Returns:
            转录结果
        """
        try:
            # 获取视频信息
            video = self.db.query(Video).filter(Video.id == video_id).first()
            if not video:
                raise ValueError(f"Video {video_id} not found")
            
            # 决定使用哪种服务
            if use_xinference is None:
                use_xinference = settings.XINFERENCE_ENABLED and xinference_service.enabled
            
            logger.info(f"Transcribing video {video_id} using {'Xinference' if use_xinference else 'local'} service")
            
            if use_xinference:
                # 使用 Xinference 服务
                result = await self._transcribe_with_xinference(
                    video, model_name, language
                )
            else:
                # 使用本地服务
                result = await self._transcribe_with_local_service(video, language)
            
            # 保存转录结果到数据库
            await self._save_transcription_to_db(video_id, result)
            
            return result
            
        except Exception as e:
            logger.error(f"Video audio transcription failed for video {video_id}: {e}")
            raise
    
    async def _transcribe_with_xinference(
        self,
        video: Video,
        model_name: Optional[str] = None,
        language: Optional[str] = None
    ) -> Dict[str, Any]:
        """使用 Xinference 进行转录"""
        try:
            result = await xinference_service.transcribe_video(
                video_path=video.file_path,
                model_name=model_name,
                language=language
            )
            
            # 标准化结果格式
            return {
                "video_id": video.id,
                "service": "xinference",
                "model": model_name or "default",
                "text": result.get("text", ""),
                "segments": result.get("segments", []),
                "language": result.get("language", language),
                "duration": result.get("duration"),
                "confidence": result.get("confidence"),
                "processing_time": result.get("processing_time"),
                "raw_result": result
            }
            
        except Exception as e:
            logger.error(f"Xinference transcription failed: {e}")
            raise
    
    async def _transcribe_with_local_service(
        self,
        video: Video,
        language: Optional[str] = None
    ) -> Dict[str, Any]:
        """使用本地服务进行转录"""
        try:
            # 首先提取音频
            audio_dir = self.file_organizer.get_video_subdirectory(video.id, 'audios')
            audio_filename = f"{os.path.splitext(video.filename)[0]}.wav"
            audio_path = audio_dir / audio_filename
            
            # 如果音频文件不存在，先提取
            if not audio_path.exists():
                from app.services.audio_service import AudioService
                audio_service = AudioService(self.db)
                audio_service.extract_audio_tracks(video.id)
            
            # 使用本地服务转录
            result = local_audio_service.process_audio(str(audio_path))
            
            # 标准化结果格式
            return {
                "video_id": video.id,
                "service": "local",
                "model": "modelscope",
                "text": result.get("text", ""),
                "segments": result.get("sentences", []),
                "language": language or "zh",
                "duration": result.get("duration"),
                "confidence": result.get("confidence"),
                "processing_time": result.get("processing_time"),
                "raw_result": result
            }
            
        except Exception as e:
            logger.error(f"Local transcription failed: {e}")
            raise
    
    async def _save_transcription_to_db(
        self,
        video_id: int,
        transcription_result: Dict[str, Any]
    ):
        """保存转录结果到数据库"""
        try:
            # 保存字幕数据
            subtitle = Subtitle(
                video_id=video_id,
                language=transcription_result.get("language", "zh"),
                content=transcription_result.get("text", ""),
                format="text",
                source="auto_generated",
                confidence=transcription_result.get("confidence"),
                metadata={
                    "service": transcription_result.get("service"),
                    "model": transcription_result.get("model"),
                    "segments": transcription_result.get("segments", []),
                    "processing_time": transcription_result.get("processing_time")
                }
            )
            
            self.db.add(subtitle)
            self.db.commit()
            
            logger.info(f"Transcription saved to database for video {video_id}")
            
        except Exception as e:
            logger.error(f"Failed to save transcription to database: {e}")
            self.db.rollback()
            raise
    
    async def compare_transcription_services(
        self,
        video_id: int,
        model_name: Optional[str] = None,
        language: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        比较不同转录服务的结果
        
        Args:
            video_id: 视频ID
            model_name: Xinference 模型名称
            language: 语言代码
            
        Returns:
            比较结果
        """
        try:
            video = self.db.query(Video).filter(Video.id == video_id).first()
            if not video:
                raise ValueError(f"Video {video_id} not found")
            
            results = {}
            
            # 使用 Xinference 转录
            if settings.XINFERENCE_ENABLED and xinference_service.enabled:
                try:
                    xinference_result = await self._transcribe_with_xinference(
                        video, model_name, language
                    )
                    results["xinference"] = xinference_result
                except Exception as e:
                    logger.error(f"Xinference transcription failed: {e}")
                    results["xinference"] = {"error": str(e)}
            
            # 使用本地服务转录
            try:
                local_result = await self._transcribe_with_local_service(video, language)
                results["local"] = local_result
            except Exception as e:
                logger.error(f"Local transcription failed: {e}")
                results["local"] = {"error": str(e)}
            
            # 计算比较指标
            comparison = self._compare_transcription_results(results)
            
            return {
                "video_id": video_id,
                "results": results,
                "comparison": comparison
            }
            
        except Exception as e:
            logger.error(f"Transcription comparison failed: {e}")
            raise
    
    def _compare_transcription_results(
        self,
        results: Dict[str, Dict[str, Any]]
    ) -> Dict[str, Any]:
        """比较转录结果"""
        comparison = {}
        
        # 提取有效结果
        valid_results = {k: v for k, v in results.items() if "error" not in v}
        
        if len(valid_results) < 2:
            return {"message": "Need at least 2 valid results for comparison"}
        
        # 比较文本长度
        text_lengths = {k: len(v.get("text", "")) for k, v in valid_results.items()}
        comparison["text_lengths"] = text_lengths
        
        # 比较处理时间
        processing_times = {
            k: v.get("processing_time", 0) for k, v in valid_results.items()
        }
        comparison["processing_times"] = processing_times
        
        # 比较置信度
        confidences = {
            k: v.get("confidence", 0) for k, v in valid_results.items()
        }
        comparison["confidences"] = confidences
        
        # 文本相似度比较（如果有多个结果）
        if len(valid_results) == 2:
            texts = list(valid_results.values())
            text1 = texts[0].get("text", "")
            text2 = texts[1].get("text", "")
            
            # 简单的相似度计算（可以使用更复杂的算法）
            similarity = self._calculate_text_similarity(text1, text2)
            comparison["text_similarity"] = similarity
        
        return comparison
    
    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """计算文本相似度"""
        try:
            # 简单的字符级相似度
            if not text1 and not text2:
                return 1.0
            if not text1 or not text2:
                return 0.0
            
            # 使用最长公共子序列
            def lcs_length(s1, s2):
                m, n = len(s1), len(s2)
                dp = [[0] * (n + 1) for _ in range(m + 1)]
                
                for i in range(1, m + 1):
                    for j in range(1, n + 1):
                        if s1[i-1] == s2[j-1]:
                            dp[i][j] = dp[i-1][j-1] + 1
                        else:
                            dp[i][j] = max(dp[i-1][j], dp[i][j-1])
                
                return dp[m][n]
            
            lcs_len = lcs_length(text1, text2)
            max_len = max(len(text1), len(text2))
            
            return lcs_len / max_len if max_len > 0 else 0.0
            
        except Exception as e:
            logger.error(f"Text similarity calculation failed: {e}")
            return 0.0
    
    async def get_transcription_history(
        self,
        video_id: int
    ) -> List[Dict[str, Any]]:
        """获取转录历史"""
        try:
            subtitles = self.db.query(Subtitle).filter(
                Subtitle.video_id == video_id,
                Subtitle.source == "auto_generated"
            ).order_by(Subtitle.created_at.desc()).all()
            
            history = []
            for subtitle in subtitles:
                history.append({
                    "id": subtitle.id,
                    "language": subtitle.language,
                    "content": subtitle.content,
                    "confidence": subtitle.confidence,
                    "service": subtitle.metadata.get("service") if subtitle.metadata else None,
                    "model": subtitle.metadata.get("model") if subtitle.metadata else None,
                    "created_at": subtitle.created_at.isoformat()
                })
            
            return history
            
        except Exception as e:
            logger.error(f"Failed to get transcription history: {e}")
            raise


# 创建服务实例的工厂函数
def create_enhanced_audio_service(db: Session) -> EnhancedAudioService:
    """创建增强音频服务实例"""
    return EnhancedAudioService(db)
