"""
Xinference 统一服务管理器
提供所有 Xinference 模型服务的统一入口
"""

from typing import Dict, List, Optional, Any
from loguru import logger

from app.core.config import settings
from app.core.xinference_config import ModelType, model_registry
from app.services.xinference_client_service import (
    xinference_client, 
    initialize_xinference, 
    cleanup_xinference
)
from app.services.xinference_llm_service import xinference_llm_service
from app.services.xinference_audio_service import xinference_audio_service
from app.services.xinference_embedding_service import xinference_embedding_service


class XinferenceService:
    """Xinference 统一服务管理器"""
    
    def __init__(self):
        self.llm = xinference_llm_service
        self.audio = xinference_audio_service
        self.embedding = xinference_embedding_service
        self.client = xinference_client
        self._initialized = False
    
    @property
    def enabled(self) -> bool:
        """检查 Xinference 是否启用"""
        return settings.XINFERENCE_ENABLED
    
    async def initialize(self):
        """初始化 Xinference 服务"""
        if not self.enabled:
            logger.info("Xinference is disabled, skipping initialization")
            return
        
        if self._initialized:
            logger.info("Xinference already initialized")
            return
        
        try:
            await initialize_xinference()
            self._initialized = True
            logger.info("Xinference service initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Xinference service: {e}")
            raise
    
    async def cleanup(self):
        """清理 Xinference 服务"""
        if not self._initialized:
            return
        
        try:
            await cleanup_xinference()
            self._initialized = False
            logger.info("Xinference service cleaned up successfully")
        except Exception as e:
            logger.error(f"Failed to cleanup Xinference service: {e}")
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        if not self.enabled:
            return {
                "status": "disabled",
                "message": "Xinference is disabled"
            }
        
        try:
            is_healthy = await self.client.health_check()
            models = await self.client.list_models()
            
            return {
                "status": "healthy" if is_healthy else "unhealthy",
                "initialized": self._initialized,
                "base_url": settings.XINFERENCE_BASE_URL,
                "running_models": len(models),
                "models": models
            }
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    async def list_available_models(self) -> Dict[str, List[Dict[str, Any]]]:
        """列出所有可用模型"""
        result = {}
        
        for model_type in ModelType:
            models = model_registry.get_models_by_type(model_type)
            result[model_type.value] = [
                {
                    "name": model.name,
                    "description": model.description,
                    "enabled": model.enabled,
                    "auto_launch": model.auto_launch,
                    "engine": model.engine.value if model.engine else None,
                    "format": model.format.value if model.format else None,
                    "size_in_billions": model.size_in_billions,
                    "dimensions": model.dimensions,
                    "multilingual": model.multilingual,
                    "family": model.family
                }
                for model in models
            ]
        
        return result
    
    async def get_running_models(self) -> List[Dict[str, Any]]:
        """获取正在运行的模型"""
        if not self.enabled:
            return []
        
        try:
            return await self.client.list_models()
        except Exception as e:
            logger.error(f"Failed to get running models: {e}")
            return []
    
    async def launch_model_by_name(
        self, 
        model_name: str, 
        **kwargs
    ) -> Optional[str]:
        """根据名称启动模型"""
        if not self.enabled:
            raise RuntimeError("Xinference is disabled")
        
        # 查找模型配置
        model_config = model_registry.get_model_by_name(model_name)
        if not model_config:
            raise ValueError(f"Model {model_name} not found in registry")
        
        return await self.client.launch_model(
            model_name, model_config.model_type, **kwargs
        )
    
    async def terminate_model_by_uid(self, model_uid: str) -> bool:
        """根据 UID 停止模型"""
        if not self.enabled:
            raise RuntimeError("Xinference is disabled")
        
        return await self.client.terminate_model(model_uid)
    
    async def get_model_info(self, model_uid: str) -> Optional[Dict[str, Any]]:
        """获取模型信息"""
        if not self.enabled:
            return None
        
        return await self.client.get_model_status(model_uid)
    
    # LLM 相关方法
    async def chat(
        self, 
        messages: List[Dict[str, str]], 
        model_name: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """LLM 对话"""
        if not self.enabled:
            raise RuntimeError("Xinference is disabled")
        
        return await self.llm.chat(messages, model_name, **kwargs)
    
    async def generate_text(
        self, 
        prompt: str, 
        model_name: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """文本生成"""
        if not self.enabled:
            raise RuntimeError("Xinference is disabled")
        
        return await self.llm.generate(prompt, model_name, **kwargs)
    
    async def analyze_video_frame(
        self,
        frame_path: str,
        question: str = "请详细描述这张图片的内容",
        model_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """分析视频帧"""
        if not self.enabled:
            raise RuntimeError("Xinference is disabled")
        
        return await self.llm.analyze_video_frame(frame_path, question, model_name)
    
    # Audio 相关方法
    async def transcribe_audio(
        self,
        audio_file_path: str,
        model_name: Optional[str] = None,
        language: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """语音转文字"""
        if not self.enabled:
            raise RuntimeError("Xinference is disabled")
        
        return await self.audio.transcribe_file(
            audio_file_path, model_name, language, **kwargs
        )
    
    async def transcribe_video(
        self,
        video_path: str,
        model_name: Optional[str] = None,
        language: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """转录视频中的语音"""
        if not self.enabled:
            raise RuntimeError("Xinference is disabled")
        
        return await self.audio.transcribe_video(
            video_path, model_name, language, **kwargs
        )
    
    # Embedding 相关方法
    async def create_embedding(
        self,
        text: str,
        model_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """创建文本向量"""
        if not self.enabled:
            raise RuntimeError("Xinference is disabled")
        
        return await self.embedding.create_embedding(text, model_name)
    
    async def compute_similarity(
        self,
        text1: str,
        text2: str,
        model_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """计算文本相似度"""
        if not self.enabled:
            raise RuntimeError("Xinference is disabled")
        
        return await self.embedding.compute_similarity(text1, text2, model_name)
    
    async def semantic_search(
        self,
        query: str,
        documents: List[Dict[str, Any]],
        text_field: str = "content",
        top_k: int = 10,
        model_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """语义搜索"""
        if not self.enabled:
            raise RuntimeError("Xinference is disabled")
        
        return await self.embedding.semantic_search(
            query, documents, text_field, top_k, model_name
        )
    
    # 批量处理方法
    async def batch_transcribe(
        self,
        file_paths: List[str],
        model_name: Optional[str] = None,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """批量转录音频"""
        if not self.enabled:
            raise RuntimeError("Xinference is disabled")
        
        return await self.audio.batch_transcribe(file_paths, model_name, **kwargs)
    
    async def batch_embedding(
        self,
        texts: List[str],
        batch_size: int = 100,
        model_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """批量创建向量"""
        if not self.enabled:
            raise RuntimeError("Xinference is disabled")
        
        return await self.embedding.get_text_embeddings_batch(
            texts, batch_size, model_name
        )
    
    # 维护方法
    async def cleanup_idle_models(self, idle_timeout: int = 3600):
        """清理空闲模型"""
        if not self.enabled:
            return
        
        await self.client.cleanup_idle_models(idle_timeout)
    
    async def auto_launch_models(self):
        """自动启动模型"""
        if not self.enabled:
            return
        
        await self.client.auto_launch_models()


# 全局 Xinference 服务实例
xinference_service = XinferenceService()
