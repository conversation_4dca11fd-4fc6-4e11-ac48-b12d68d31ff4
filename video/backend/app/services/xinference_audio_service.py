"""
Xinference Audio 服务
提供语音识别和音频处理功能
"""

import os
import tempfile
from typing import Dict, List, Optional, Any, BinaryIO
from loguru import logger
import json

from app.core.config import settings
from app.core.xinference_config import ModelType, get_default_model_name
from app.services.xinference_client_service import xinference_client


class XinferenceAudioService:
    """Xinference Audio 服务"""
    
    def __init__(self):
        self.default_model = get_default_model_name(ModelType.AUDIO)
    
    async def transcribe(
        self,
        audio_file: BinaryIO,
        model_name: Optional[str] = None,
        language: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        语音转文字
        
        Args:
            audio_file: 音频文件对象
            model_name: 模型名称，默认使用配置的默认模型
            language: 语言代码 (可选)
            **kwargs: 其他转录参数
            
        Returns:
            转录结果
        """
        try:
            model_name = model_name or self.default_model
            
            model_uid = model_name

            # 确保模型可用 TODO
            # model_uid = await xinference_client.ensure_model_available(
            #     model_name, ModelType.AUDIO
            # )
            
            # if not model_uid:
            #     raise RuntimeError(f"Failed to launch Audio model: {model_name}")
            
            # 构建请求参数
            files = {"file": audio_file}
            data = {"model": model_uid}
            
            if language:
                data["language"] = language
            
            # 添加其他参数
            data.update(kwargs)
            
            # 发送转录请求
            client = await xinference_client._get_client()
            response = await client.post(
                "/v1/audio/transcriptions",
                files=files,
                data=data
            )
            response.raise_for_status()
            result = response.json()
            
            logger.info(f"Audio transcription completed for model {model_name}")
            return result
            
        except Exception as e:
            logger.error(f"Audio transcription failed: {e}")
            raise
    
    async def transcribe_file(
        self,
        file_path: str,
        model_name: Optional[str] = None,
        language: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        转录音频文件
        
        Args:
            file_path: 音频文件路径
            model_name: 模型名称
            language: 语言代码
            **kwargs: 其他转录参数
            
        Returns:
            转录结果
        """
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"Audio file not found: {file_path}")
            
            with open(file_path, "rb") as audio_file:
                result = await self.transcribe(
                    audio_file, model_name, language, **kwargs
                )
            
            # 添加文件信息
            result["file_path"] = file_path
            result["file_size"] = os.path.getsize(file_path)
            
            return result
            
        except Exception as e:
            logger.error(f"Audio file transcription failed: {e}")
            raise
    
    async def translate(
        self,
        audio_file: BinaryIO,
        target_language: str = "en",
        model_name: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        语音翻译
        
        Args:
            audio_file: 音频文件对象
            target_language: 目标语言代码
            model_name: 模型名称
            **kwargs: 其他翻译参数
            
        Returns:
            翻译结果
        """
        try:
            model_name = model_name or self.default_model
            
            # 确保模型可用
            model_uid = await xinference_client.ensure_model_available(
                model_name, ModelType.AUDIO
            )
            
            if not model_uid:
                raise RuntimeError(f"Failed to launch Audio model: {model_name}")
            
            # 构建请求参数
            files = {"file": audio_file}
            data = {
                "model": model_uid,
                "language": target_language
            }
            
            # 添加其他参数
            data.update(kwargs)
            
            # 发送翻译请求
            client = await xinference_client._get_client()
            response = await client.post(
                "/v1/audio/translations",
                files=files,
                data=data
            )
            response.raise_for_status()
            result = response.json()

            logger.info(f"Audio translation completed for model {model_name}")
            return result
            
        except Exception as e:
            logger.error(f"Audio translation failed: {e}")
            raise
    
    async def extract_audio_from_video(
        self,
        video_path: str,
        output_path: Optional[str] = None,
        format: str = "wav"
    ) -> str:
        """
        从视频中提取音频
        
        Args:
            video_path: 视频文件路径
            output_path: 输出音频文件路径
            format: 音频格式
            
        Returns:
            提取的音频文件路径
        """
        try:
            import subprocess
            
            if not os.path.exists(video_path):
                raise FileNotFoundError(f"Video file not found: {video_path}")
            
            if not output_path:
                # 生成临时文件路径
                temp_dir = tempfile.gettempdir()
                base_name = os.path.splitext(os.path.basename(video_path))[0]
                output_path = os.path.join(temp_dir, f"{base_name}.{format}")
            
            # 使用 ffmpeg 提取音频
            cmd = [
                "ffmpeg",
                "-i", video_path,
                "-vn",  # 不包含视频
                "-acodec", "pcm_s16le" if format == "wav" else "mp3",
                "-ar", "16000",  # 采样率
                "-ac", "1",  # 单声道
                "-y",  # 覆盖输出文件
                output_path
            ]
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                check=True
            )
            
            logger.info(f"Audio extracted from video: {video_path} -> {output_path}")
            return output_path
            
        except subprocess.CalledProcessError as e:
            logger.error(f"FFmpeg error: {e.stderr}")
            raise RuntimeError(f"Failed to extract audio: {e.stderr}")
        except Exception as e:
            logger.error(f"Audio extraction failed: {e}")
            raise
    
    async def transcribe_video(
        self,
        video_path: str,
        model_name: Optional[str] = None,
        language: Optional[str] = None,
        cleanup_audio: bool = True,
        **kwargs
    ) -> Dict[str, Any]:
        """
        转录视频中的语音
        
        Args:
            video_path: 视频文件路径
            model_name: 模型名称
            language: 语言代码
            cleanup_audio: 是否清理临时音频文件
            **kwargs: 其他转录参数
            
        Returns:
            转录结果
        """
        temp_audio_path = None
        try:
            # 从视频提取音频
            temp_audio_path = await self.extract_audio_from_video(video_path)
            
            # 转录音频
            result = await self.transcribe_file(
                temp_audio_path, model_name, language, **kwargs
            )
            
            # 添加视频信息
            result["video_path"] = video_path
            result["extracted_audio_path"] = temp_audio_path
            
            return result
            
        except Exception as e:
            logger.error(f"Video transcription failed: {e}")
            raise
        finally:
            # 清理临时音频文件
            if cleanup_audio and temp_audio_path and os.path.exists(temp_audio_path):
                try:
                    os.remove(temp_audio_path)
                    logger.debug(f"Cleaned up temporary audio file: {temp_audio_path}")
                except Exception as e:
                    logger.warning(f"Failed to cleanup audio file: {e}")
    
    async def batch_transcribe(
        self,
        file_paths: List[str],
        model_name: Optional[str] = None,
        language: Optional[str] = None,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        批量转录音频文件
        
        Args:
            file_paths: 音频文件路径列表
            model_name: 模型名称
            language: 语言代码
            **kwargs: 其他转录参数
            
        Returns:
            转录结果列表
        """
        results = []
        
        for file_path in file_paths:
            try:
                result = await self.transcribe_file(
                    file_path, model_name, language, **kwargs
                )
                results.append(result)
                logger.info(f"Batch transcription completed for: {file_path}")
                
            except Exception as e:
                logger.error(f"Batch transcription failed for {file_path}: {e}")
                results.append({
                    "file_path": file_path,
                    "error": str(e),
                    "success": False
                })
        
        return results
    
    async def get_audio_duration(self, file_path: str) -> float:
        """
        获取音频文件时长
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            音频时长（秒）
        """
        try:
            import subprocess
            
            cmd = [
                "ffprobe",
                "-v", "quiet",
                "-show_entries", "format=duration",
                "-of", "csv=p=0",
                file_path
            ]
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                check=True
            )
            
            duration = float(result.stdout.strip())
            return duration
            
        except Exception as e:
            logger.error(f"Failed to get audio duration: {e}")
            return 0.0


# 全局 Audio 服务实例
xinference_audio_service = XinferenceAudioService()
