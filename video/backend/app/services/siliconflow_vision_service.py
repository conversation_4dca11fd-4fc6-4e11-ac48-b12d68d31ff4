"""
硅基流动视觉理解模型服务
接入硅基流动的视觉理解模型，支持多图理解能力
文档地址：https://docs.siliconflow.cn/cn/userguide/capabilities/vision
"""

import asyncio
import aiohttp
import base64
import time
from typing import List, Dict, Any, Optional
from pathlib import Path
import logging

from app.services.vision_model_interface import (
    VisionModelInterface, 
    VisionModelError, 
    VisionModelConfig,
    VisionAnalysisResult
)
from app.core.config import settings

logger = logging.getLogger(__name__)


class SiliconFlowVisionService(VisionModelInterface):
    """硅基流动视觉理解服务"""
    
    def __init__(self):
        config = VisionModelConfig(
            api_key=getattr(settings, 'SILICONFLOW_API_KEY', ''),
            base_url=getattr(settings, 'SILICONFLOW_BASE_URL', 'https://api.siliconflow.cn/v1'),
            timeout=30,
            max_retries=3,
            max_images_per_request=10
        )
        super().__init__("SiliconFlow-Vision", config.to_dict())
        self.config_obj = config
        self.session = None
    
    async def initialize(self) -> bool:
        """初始化服务"""
        try:
            if not self.config_obj.api_key:
                logger.error("SiliconFlow API key not configured")
                return False
            
            # 创建HTTP会话
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=self.config_obj.timeout),
                headers={
                    "Authorization": f"Bearer {self.config_obj.api_key}",
                    "Content-Type": "application/json"
                }
            )
            
            # 测试连接
            await self._test_connection()
            
            self.is_initialized = True
            logger.info("SiliconFlow vision service initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize SiliconFlow vision service: {e}")
            if self.session:
                await self.session.close()
                self.session = None
            return False
    
    async def _test_connection(self):
        """测试API连接"""
        try:
            # 使用一个简单的请求测试连接
            url = f"{self.config_obj.base_url}/models"
            async with self.session.get(url) as response:
                if response.status != 200:
                    raise VisionModelError(f"API connection test failed: {response.status}")
        except Exception as e:
            raise VisionModelError(f"Failed to connect to SiliconFlow API: {e}")
    
    async def analyze_single_image(
        self, 
        image_path: str, 
        question: str = "请详细描述这张图片的内容",
        **kwargs
    ) -> Dict[str, Any]:
        """分析单张图片"""
        if not self.is_initialized:
            raise VisionModelError("Service not initialized", self.model_name)
        
        if not self.validate_image_path(image_path):
            raise VisionModelError(f"Invalid image path: {image_path}", self.model_name)
        
        start_time = time.time()
        
        try:
            # 编码图片
            image_base64 = await self._encode_image(image_path)
            
            # 构建请求
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": question
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{image_base64}"
                            }
                        }
                    ]
                }
            ]
            
            # 发送请求
            result = await self._make_request(messages)
            
            processing_time = time.time() - start_time
            
            return VisionAnalysisResult(
                model_name=self.model_name,
                analysis_type="single_image",
                content=result.get("content", ""),
                confidence=result.get("confidence"),
                metadata={
                    "image_path": image_path,
                    "question": question,
                    **kwargs
                },
                processing_time=processing_time
            ).to_dict()
            
        except Exception as e:
            logger.error(f"Error analyzing single image: {e}")
            raise VisionModelError(f"Single image analysis failed: {e}", self.model_name)
    
    async def analyze_multiple_images(
        self, 
        image_paths: List[str], 
        question: str = "请分析这些图片的内容和关联性",
        **kwargs
    ) -> Dict[str, Any]:
        """分析多张图片"""
        if not self.is_initialized:
            raise VisionModelError("Service not initialized", self.model_name)
        
        # 验证图片路径
        valid_paths = self.validate_image_paths(image_paths)
        if not valid_paths:
            raise VisionModelError("No valid image paths provided", self.model_name)
        
        # 限制图片数量
        if len(valid_paths) > self.config_obj.max_images_per_request:
            valid_paths = valid_paths[:self.config_obj.max_images_per_request]
            logger.warning(f"Limited to {self.config_obj.max_images_per_request} images")
        
        start_time = time.time()
        
        try:
            # 构建消息内容
            content = [{"type": "text", "text": question}]
            
            # 添加所有图片
            for image_path in valid_paths:
                image_base64 = await self._encode_image(image_path)
                content.append({
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpeg;base64,{image_base64}"
                    }
                })
            
            messages = [{"role": "user", "content": content}]
            
            # 发送请求
            result = await self._make_request(messages)
            
            processing_time = time.time() - start_time
            
            return VisionAnalysisResult(
                model_name=self.model_name,
                analysis_type="multiple_images",
                content=result.get("content", ""),
                confidence=result.get("confidence"),
                metadata={
                    "image_paths": valid_paths,
                    "image_count": len(valid_paths),
                    "question": question,
                    **kwargs
                },
                processing_time=processing_time
            ).to_dict()
            
        except Exception as e:
            logger.error(f"Error analyzing multiple images: {e}")
            raise VisionModelError(f"Multiple images analysis failed: {e}", self.model_name)
    
    async def compare_images(
        self, 
        image_paths: List[str], 
        question: str = "请比较这些图片的异同点",
        **kwargs
    ) -> Dict[str, Any]:
        """比较多张图片"""
        # 对于比较任务，我们使用多图分析的相同逻辑
        return await self.analyze_multiple_images(image_paths, question, **kwargs)
    
    async def _make_request(self, messages: List[Dict[str, Any]]) -> Dict[str, Any]:
        """发送API请求"""
        url = f"{self.config_obj.base_url}/chat/completions"
        
        payload = {
            "model": "Pro/Qwen/Qwen2-VL-72B-Instruct",  # 硅基流动的视觉模型
            "messages": messages,
            "max_tokens": 2000,
            "temperature": 0.7
        }
        
        for attempt in range(self.config_obj.max_retries):
            try:
                async with self.session.post(url, json=payload) as response:
                    if response.status == 200:
                        data = await response.json()
                        if "choices" in data and data["choices"]:
                            content = data["choices"][0]["message"]["content"]
                            return {"content": content}
                        else:
                            raise VisionModelError("Invalid response format")
                    else:
                        error_text = await response.text()
                        raise VisionModelError(f"API request failed: {response.status} - {error_text}")
                        
            except asyncio.TimeoutError:
                if attempt < self.config_obj.max_retries - 1:
                    await asyncio.sleep(2 ** attempt)  # 指数退避
                    continue
                raise VisionModelError("Request timeout")
            except Exception as e:
                if attempt < self.config_obj.max_retries - 1:
                    await asyncio.sleep(2 ** attempt)
                    continue
                raise
    
    async def _encode_image(self, image_path: str) -> str:
        """将图片编码为base64"""
        try:
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
        except Exception as e:
            raise VisionModelError(f"Failed to encode image {image_path}: {e}")
    
    def get_capabilities(self) -> List[str]:
        """获取模型能力列表"""
        return [
            "single_image_analysis",
            "multiple_images_analysis", 
            "image_comparison",
            "text_recognition",
            "object_detection",
            "scene_understanding"
        ]
    
    async def __aenter__(self):
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
