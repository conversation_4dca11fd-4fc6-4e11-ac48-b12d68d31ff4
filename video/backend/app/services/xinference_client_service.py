"""
Xinference 客户端服务
提供统一的 Xinference 连接管理和模型操作接口
"""

import asyncio
import httpx
from typing import Dict, List, Optional, Any, Union
from loguru import logger
from contextlib import asynccontextmanager
import json
import time
from dataclasses import dataclass

from app.core.config import settings
from app.core.xinference_config import ModelType, ModelConfig, model_registry


@dataclass
class ModelInstance:
    """模型实例信息"""
    model_uid: str
    model_name: str
    model_type: ModelType
    status: str
    created_at: float
    last_used: float


class XinferenceClientService:
    """Xinference 客户端服务"""
    
    def __init__(self):
        self.base_url = settings.XINFERENCE_BASE_URL
        self.api_key = settings.XINFERENCE_API_KEY
        self.timeout = settings.XINFERENCE_TIMEOUT
        self.max_retries = settings.XINFERENCE_MAX_RETRIES
        self._client: Optional[httpx.AsyncClient] = None
        self._model_instances: Dict[str, ModelInstance] = {}
        self._connection_pool_size = settings.XINFERENCE_CONNECTION_POOL_SIZE
        
    async def _get_client(self) -> httpx.AsyncClient:
        """获取 HTTP 客户端"""
        if self._client is None:
            headers = {}
            if self.api_key:
                headers["Authorization"] = f"Bearer {self.api_key}"
                
            limits = httpx.Limits(
                max_keepalive_connections=self._connection_pool_size,
                max_connections=self._connection_pool_size * 2
            )
            
            self._client = httpx.AsyncClient(
                base_url=self.base_url,
                headers=headers,
                timeout=self.timeout,
                limits=limits
            )
        return self._client
    
    async def close(self):
        """关闭客户端连接"""
        if self._client:
            await self._client.aclose()
            self._client = None
    
    async def _make_request(
        self, 
        method: str, 
        endpoint: str, 
        **kwargs
    ) -> Dict[str, Any]:
        """发送 HTTP 请求"""
        client = await self._get_client()
        
        for attempt in range(self.max_retries + 1):
            try:
                response = await client.request(method, endpoint, **kwargs)
                response.raise_for_status()
                
                if response.headers.get("content-type", "").startswith("application/json"):
                    return response.json()
                else:
                    return {"content": response.content, "headers": dict(response.headers)}
                    
            except httpx.HTTPStatusError as e:
                logger.error(f"HTTP error {e.response.status_code}: {e.response.text}")
                if attempt == self.max_retries:
                    raise
                await asyncio.sleep(2 ** attempt)
                
            except httpx.RequestError as e:
                logger.error(f"Request error: {e}")
                if attempt == self.max_retries:
                    raise
                await asyncio.sleep(2 ** attempt)
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            await self._make_request("GET", "/v1/models")
            return True
        except Exception as e:
            logger.error(f"Xinference health check failed: {e}")
            return False
    
    async def list_models(self) -> List[Dict[str, Any]]:
        """列出所有已启动的模型"""
        try:
            response = await self._make_request("GET", "/v1/models")
            return response.get("data", [])
        except Exception as e:
            logger.error(f"Failed to list models: {e}")
            return []
    
    async def launch_model(
        self, 
        model_name: str, 
        model_type: ModelType,
        **kwargs
    ) -> Optional[str]:
        """启动模型"""
        try:
            # 获取模型配置
            model_config = model_registry.get_model_by_name(model_name)
            if not model_config:
                raise ValueError(f"Model {model_name} not found in registry")
            
            # 构建启动参数
            launch_params = {
                "model_name": model_name,
                "model_type": model_type.value,
                **model_config.launch_params,
                **kwargs
            }
            
            # 添加模型特定参数
            if model_config.engine:
                launch_params["model_engine"] = model_config.engine.value
            if model_config.format:
                launch_params["model_format"] = model_config.format.value
            if model_config.size_in_billions:
                launch_params["model_size_in_billions"] = model_config.size_in_billions
            if model_config.quantization:
                launch_params["quantization"] = model_config.quantization
            
            logger.info(f"Launching model {model_name} with params: {launch_params}")
            
            response = await self._make_request(
                "POST", 
                "/v1/models", 
                json=launch_params
            )
            
            model_uid = response.get("model_uid")
            if model_uid:
                # 记录模型实例
                self._model_instances[model_uid] = ModelInstance(
                    model_uid=model_uid,
                    model_name=model_name,
                    model_type=model_type,
                    status="running",
                    created_at=time.time(),
                    last_used=time.time()
                )
                logger.info(f"Model {model_name} launched successfully with UID: {model_uid}")
            
            return model_uid
            
        except Exception as e:
            logger.error(f"Failed to launch model {model_name}: {e}")
            return None
    
    async def terminate_model(self, model_uid: str) -> bool:
        """停止模型"""
        try:
            await self._make_request("DELETE", f"/v1/models/{model_uid}")
            
            # 移除模型实例记录
            if model_uid in self._model_instances:
                del self._model_instances[model_uid]
                
            logger.info(f"Model {model_uid} terminated successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to terminate model {model_uid}: {e}")
            return False
    
    async def get_model_status(self, model_uid: str) -> Optional[Dict[str, Any]]:
        """获取模型状态"""
        try:
            response = await self._make_request("GET", f"/v1/models/{model_uid}")
            return response
        except Exception as e:
            logger.error(f"Failed to get model status for {model_uid}: {e}")
            return None
    
    def get_model_instance(self, model_uid: str) -> Optional[ModelInstance]:
        """获取模型实例信息"""
        return self._model_instances.get(model_uid)
    
    def get_model_instances_by_type(self, model_type: ModelType) -> List[ModelInstance]:
        """根据类型获取模型实例"""
        return [
            instance for instance in self._model_instances.values()
            if instance.model_type == model_type
        ]
    
    async def ensure_model_available(
        self, 
        model_name: str, 
        model_type: ModelType
    ) -> Optional[str]:
        """确保模型可用，如果未启动则自动启动"""
        # 检查是否已有运行中的实例
        for instance in self._model_instances.values():
            if (instance.model_name == model_name and 
                instance.model_type == model_type and 
                instance.status == "running"):
                # 更新最后使用时间
                instance.last_used = time.time()
                return instance.model_uid
        
        # 启动新实例
        return await self.launch_model(model_name, model_type)
    
    async def cleanup_idle_models(self, idle_timeout: int = 3600):
        """清理空闲模型"""
        current_time = time.time()
        idle_models = []
        
        for model_uid, instance in self._model_instances.items():
            if current_time - instance.last_used > idle_timeout:
                idle_models.append(model_uid)
        
        for model_uid in idle_models:
            logger.info(f"Terminating idle model: {model_uid}")
            await self.terminate_model(model_uid)
    
    async def auto_launch_models(self):
        """自动启动配置为自动启动的模型"""
        auto_launch_models = [
            model for model in model_registry.get_enabled_models()
            if model.auto_launch
        ]
        
        for model_config in auto_launch_models:
            logger.info(f"Auto-launching model: {model_config.name}")
            await self.launch_model(model_config.name, model_config.model_type)


# 全局客户端实例
xinference_client = XinferenceClientService()


@asynccontextmanager
async def get_xinference_client():
    """获取 Xinference 客户端的上下文管理器"""
    try:
        yield xinference_client
    finally:
        # 可以在这里添加清理逻辑
        pass


async def initialize_xinference():
    """初始化 Xinference 服务"""
    if not settings.XINFERENCE_ENABLED:
        logger.info("Xinference is disabled")
        return
    
    logger.info("Initializing Xinference service...")
    
    # 健康检查
    if not await xinference_client.health_check():
        logger.error("Xinference service is not available")
        return
    
    # 自动启动模型
    await xinference_client.auto_launch_models()
    
    logger.info("Xinference service initialized successfully")


async def cleanup_xinference():
    """清理 Xinference 服务"""
    logger.info("Cleaning up Xinference service...")
    await xinference_client.close()
    logger.info("Xinference service cleaned up")
