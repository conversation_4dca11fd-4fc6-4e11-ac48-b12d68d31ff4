"""
FFmpeg and FFprobe service for video analysis
"""

import os
import json
import subprocess
import shutil
from typing import Dict, List, Optional, Tuple
from loguru import logger
from app.utils.file_organization import file_organizer


class FFmpegService:
    """Service for FFmpeg and FFprobe operations"""
    
    def __init__(self):
        self.ffmpeg_path = self._find_executable("ffmpeg")
        self.ffprobe_path = self._find_executable("ffprobe")
        
    def _find_executable(self, name: str) -> Optional[str]:
        """Find executable path"""
        path = shutil.which(name)
        if not path:
            logger.warning(f"{name} not found in PATH")
        return path
    
    def check_dependencies(self) -> Dict[str, bool]:
        """Check if FFmpeg and FFprobe are available"""
        return {
            "ffmpeg": self.ffmpeg_path is not None,
            "ffprobe": self.ffprobe_path is not None
        }
    
    # 在用
    def get_video_metadata(self, video_path: str) -> Dict:
        """
        Extract video metadata using ffprobe
        Similar to: ffprobe -v quiet -print_format json -show_format -show_streams video.mp4
        """
        if not self.ffprobe_path:
            raise RuntimeError("ffprobe not available")
        
        if not os.path.exists(video_path):
            raise FileNotFoundError(f"Video file not found: {video_path}")
        
        try:
            cmd = [
                self.ffprobe_path,
                "-v", "quiet",
                "-print_format", "json",
                "-show_format",
                "-show_streams",
                video_path
            ]
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                check=True
            )
            
            metadata = json.loads(result.stdout)
            return self._process_metadata(metadata)
            
        except subprocess.CalledProcessError as e:
            logger.error(f"ffprobe failed: {e.stderr}")
            raise RuntimeError(f"Failed to analyze video: {e.stderr}")
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse ffprobe output: {e}")
            raise RuntimeError("Failed to parse video metadata")
    
    def _process_metadata(self, raw_metadata: Dict) -> Dict:
        """Process raw ffprobe metadata into structured format"""
        processed = {
            "format": {},
            "video_streams": [],
            "audio_streams": [],
            "subtitle_streams": []
        }
        
        # Process format information
        if "format" in raw_metadata:
            format_info = raw_metadata["format"]
            processed["format"] = {
                "filename": format_info.get("filename"),
                "format_name": format_info.get("format_name"),
                "format_long_name": format_info.get("format_long_name"),
                "duration": float(format_info.get("duration", 0)),
                "size": int(format_info.get("size", 0)),
                "bit_rate": int(format_info.get("bit_rate", 0)),
                "tags": format_info.get("tags", {})
            }
        
        # Process streams
        if "streams" in raw_metadata:
            for stream in raw_metadata["streams"]:
                stream_type = stream.get("codec_type")
                
                if stream_type == "video":
                    processed["video_streams"].append(self._process_video_stream(stream))
                elif stream_type == "audio":
                    processed["audio_streams"].append(self._process_audio_stream(stream))
                elif stream_type == "subtitle":
                    processed["subtitle_streams"].append(self._process_subtitle_stream(stream))
        
        return processed
    
    def _process_video_stream(self, stream: Dict) -> Dict:
        """Process video stream information"""
        return {
            "index": stream.get("index"),
            "codec_name": stream.get("codec_name"),
            "codec_long_name": stream.get("codec_long_name"),
            "width": stream.get("width"),
            "height": stream.get("height"),
            "fps": self._parse_fps(stream.get("r_frame_rate")),
            "bit_rate": int(stream.get("bit_rate", 0)) if stream.get("bit_rate") else None,
            "duration": float(stream.get("duration", 0)) if stream.get("duration") else None,
            "pixel_format": stream.get("pix_fmt"),
            "tags": stream.get("tags", {})
        }
    
    def _process_audio_stream(self, stream: Dict) -> Dict:
        """Process audio stream information"""
        return {
            "index": stream.get("index"),
            "codec_name": stream.get("codec_name"),
            "codec_long_name": stream.get("codec_long_name"),
            "sample_rate": int(stream.get("sample_rate", 0)) if stream.get("sample_rate") else None,
            "channels": stream.get("channels"),
            "channel_layout": stream.get("channel_layout"),
            "bit_rate": int(stream.get("bit_rate", 0)) if stream.get("bit_rate") else None,
            "duration": float(stream.get("duration", 0)) if stream.get("duration") else None,
            "tags": stream.get("tags", {})
        }
    
    def _process_subtitle_stream(self, stream: Dict) -> Dict:
        """Process subtitle stream information"""
        return {
            "index": stream.get("index"),
            "codec_name": stream.get("codec_name"),
            "codec_long_name": stream.get("codec_long_name"),
            "tags": stream.get("tags", {})
        }
    
    def _parse_fps(self, fps_str: str) -> Optional[float]:
        """Parse frame rate string like '30/1' to float"""
        if not fps_str:
            return None
        try:
            if "/" in fps_str:
                num, den = fps_str.split("/")
                return float(num) / float(den)
            return float(fps_str)
        except (ValueError, ZeroDivisionError):
            return None
    
    def extract_audio(self, video_path: str, output_path: str, stream_index: int = 0) -> bool:
        """Extract audio track from video"""
        if not self.ffmpeg_path:
            raise RuntimeError("ffmpeg not available")

        try:
            # Use absolute stream index instead of audio-relative index
            cmd = [
                self.ffmpeg_path,
                "-i", video_path,
                "-map", f"0:{stream_index}",  # Use absolute stream index
                "-acodec", "copy",
                "-y",  # Overwrite output file
                output_path
            ]

            logger.info(f"Extracting audio with command: {' '.join(cmd)}")

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                check=True
            )

            # Verify the output file exists and has content
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                if file_size > 0:
                    logger.info(f"Audio extraction successful: {output_path} ({file_size} bytes)")
                    return True
                else:
                    logger.warning(f"Audio extraction produced empty file: {output_path}")
                    return False
            else:
                logger.warning(f"Audio extraction failed: output file not created")
                return False

        except subprocess.CalledProcessError as e:
            logger.error(f"Audio extraction failed: {e.stderr}")
            return False

    # 废弃
    def extract_frames(self, video_path: str, output_dir: str, fps: float = 1.0) -> List[str]:
        """Extract frames from video at specified fps"""
        if not self.ffmpeg_path:
            raise RuntimeError("ffmpeg not available")

        os.makedirs(output_dir, exist_ok=True)

        try:
            output_pattern = os.path.join(output_dir, "frame_%06d.jpg")
            cmd = [
                self.ffmpeg_path,
                "-i", video_path,
                "-vf", f"fps={fps}",
                "-y",  # Overwrite output files
                output_pattern
            ]

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                check=True
            )

            # Get list of generated frame files
            frame_files = []
            for file in os.listdir(output_dir):
                if file.startswith("frame_") and file.endswith(".jpg"):
                    frame_files.append(os.path.join(output_dir, file))

            return sorted(frame_files)

        except subprocess.CalledProcessError as e:
            logger.error(f"Frame extraction failed: {e.stderr}")
            return []

    def extract_all_frames(self, video_path: str, video_id: int) -> List[Tuple[str, float, int, int]]:
        """Extract all frames from video with metadata"""
        if not self.ffmpeg_path:
            raise RuntimeError("ffmpeg not available")

        # Get organized frames directory
        frames_dir = file_organizer.get_video_subdirectory(video_id, 'frames')

        try:
            # First, get video metadata to understand frame structure
            metadata = self.get_video_metadata(video_path)
            video_stream = metadata.get('video_streams', [{}])[0]
            width = video_stream.get('width', 0)
            height = video_stream.get('height', 0)
            fps = video_stream.get('fps', 25.0)

            # Extract all frames using ffmpeg
            output_pattern = str(frames_dir / "frame_%06d.jpg")
            cmd = [
                self.ffmpeg_path,
                "-i", video_path,
                "-y",  # Overwrite output files
                output_pattern
            ]

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                check=True
            )

            # Get list of generated frame files with metadata
            all_frame_files = []
            for file in sorted(frames_dir.glob("frame_*.jpg")):
                # Extract frame number from filename
                frame_num = int(file.stem.split('_')[1])

                # Calculate timestamp based on frame number and fps
                timestamp = (frame_num - 1) / fps if fps > 0 else 0.0

                all_frame_files.append((str(file), timestamp, width, height))

            logger.info(f"Extracted {len(all_frame_files)} frames from {video_path}")
            return all_frame_files

        except subprocess.CalledProcessError as e:
            logger.error(f"Frame extraction failed: {e.stderr}")
            return []

    def extract_key_frames(self, video_path: str, video_id: int) -> List[Tuple[str, float, int, int]]:
        """Extract only key frames from video with metadata (deprecated - use extract_all_frames)"""
        logger.warning("extract_key_frames is deprecated, using extract_all_frames instead")
        return self.extract_all_frames(video_path, video_id)

    def get_comprehensive_video_info(self, video_path: str) -> Dict:
        """
        Extract comprehensive video information using ffprobe with all available options
        Similar to: ffprobe -v quiet -print_format json -show_streams -show_format -show_chapters -show_programs -show_packets -show_frames video.mp4

        Warning: This generates very large JSON files as it includes detailed packet and frame information
        """
        if not self.ffprobe_path:
            raise RuntimeError("ffprobe not available")

        if not os.path.exists(video_path):
            raise FileNotFoundError(f"Video file not found: {video_path}")

        try:
            cmd = [
                self.ffprobe_path,
                "-v", "quiet",
                "-print_format", "json",
                "-show_streams",
                "-show_format",
                "-show_chapters",
                "-show_programs",
                "-show_packets",
                "-show_frames",
                video_path
            ]

            logger.info(f"Running comprehensive ffprobe analysis on {video_path}")
            logger.warning("This may take a while and generate a large JSON file")

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                check=True
            )

            # Parse the JSON output
            comprehensive_data = json.loads(result.stdout)

            # Reorganize the data to match the expected format
            organized_data = self._organize_comprehensive_data(comprehensive_data)

            logger.info(f"Comprehensive analysis completed for {video_path}")
            return organized_data

        except subprocess.CalledProcessError as e:
            logger.error(f"Comprehensive ffprobe analysis failed: {e.stderr}")
            raise RuntimeError(f"Failed to analyze video comprehensively: {e.stderr}")
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse comprehensive ffprobe output: {e}")
            raise RuntimeError("Failed to parse comprehensive video data")

    def _organize_comprehensive_data(self, raw_data: Dict) -> Dict:
        """
        Organize comprehensive ffprobe data into a structured format
        """
        organized = {
            "format": raw_data.get("format", {}),
            "streams": raw_data.get("streams", []),
            "chapters": raw_data.get("chapters", []),
            "programs": raw_data.get("programs", []),
            "packets_and_frames": []
        }

        # Check if ffprobe already combined packets and frames
        if "packets_and_frames" in raw_data:
            # ffprobe already combined them when using both -show_packets and -show_frames
            organized["packets_and_frames"] = raw_data["packets_and_frames"]
            logger.info(f"Found {len(organized['packets_and_frames'])} combined packets and frames")
        else:
            # Combine packets and frames manually if they're separate
            packets = raw_data.get("packets", [])
            frames = raw_data.get("frames", [])

            logger.info(f"Found {len(packets)} packets and {len(frames)} frames")

            # Add type field to distinguish packets from frames and copy data
            for packet in packets:
                packet_copy = packet.copy()
                packet_copy["type"] = "packet"
                organized["packets_and_frames"].append(packet_copy)

            for frame in frames:
                frame_copy = frame.copy()
                frame_copy["type"] = "frame"
                organized["packets_and_frames"].append(frame_copy)

            # Sort by presentation timestamp if available
            def get_sort_key(item):
                try:
                    if item.get("type") == "packet":
                        # Try pts_time first, then pts, then default to 0
                        if "pts_time" in item:
                            return float(item["pts_time"])
                        elif "pts" in item:
                            return float(item["pts"])
                        else:
                            return 0.0
                    else:  # frame
                        # Try pts_time first, then best_effort_timestamp_time, then pts, then default to 0
                        if "pts_time" in item:
                            return float(item["pts_time"])
                        elif "best_effort_timestamp_time" in item:
                            return float(item["best_effort_timestamp_time"])
                        elif "pts" in item:
                            return float(item["pts"])
                        else:
                            return 0.0
                except (ValueError, TypeError):
                    return 0.0

            try:
                organized["packets_and_frames"].sort(key=get_sort_key)
                logger.info(f"Successfully sorted {len(organized['packets_and_frames'])} packets and frames")
            except Exception as e:
                # If sorting fails, keep original order
                logger.warning(f"Could not sort packets and frames by timestamp: {e}")

        return organized


# Global instance
ffmpeg_service = FFmpegService()
