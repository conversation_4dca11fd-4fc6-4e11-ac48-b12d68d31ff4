"""
Video analysis service for comprehensive video processing
"""

import os
import json
import time
from typing import Dict, List, Optional
from sqlalchemy.orm import Session
from loguru import logger

from app.models.task import Video, AudioTrack, VideoFrame, BitrateStats
from app.services.ffmpeg_service import ffmpeg_service
from app.services.bitrate_stats_service import BitrateStatsService
from app.utils.file_organization import file_organizer


class VideoAnalysisService:
    """Service for comprehensive video analysis"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def analyze_video_metadata(self, video_id: int) -> Dict:
        """
        Analyze video metadata using ffprobe and store in database
        """
        from app.tasks.task_logger import TaskLogger
        import time
        
        task_logger = TaskLogger("VIDEO_METADATA_SERVICE", video_id=video_id)
        task_logger.start_task(description="视频元数据分析服务")
        
        video = self.db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise ValueError(f"Video {video_id} not found")
        
        if not os.path.exists(video.file_path):
            raise FileNotFoundError(f"Video file not found: {video.file_path}")
        
        try:
            # 获取文件信息
            file_size = os.path.getsize(video.file_path)
            task_logger.log_info(f"开始分析视频文件", {
                "file_path": os.path.basename(video.file_path),
                "file_size_mb": f"{file_size / 1024 / 1024:.2f}"
            })

            # Extract metadata using ffprobe
            task_logger.start_step("提取视频元数据")
            metadata_start = time.time()
            metadata = ffmpeg_service.get_video_metadata(video.file_path)
            metadata_duration = time.time() - metadata_start
            
            task_logger.log_performance("ffprobe元数据提取", metadata_duration, 
                                       f"获取到 {len(metadata)} 个元数据字段")
            task_logger.complete_step("提取视频元数据", f"元数据字段数: {len(metadata)}")
            
            # Update video basic information
            task_logger.start_step("更新视频基础信息")
            update_start = time.time()
            self._update_video_basic_info(video, metadata)
            update_duration = time.time() - update_start
            
            task_logger.log_performance("视频信息更新", update_duration, "更新数据库中的视频基础信息")
            task_logger.complete_step("更新视频基础信息", "基础信息已更新")
            
            # Process audio tracks
            task_logger.start_step("处理音频轨道信息")
            audio_streams = metadata.get("audio_streams", [])
            audio_start = time.time()
            self._process_audio_tracks(video, audio_streams)
            audio_duration = time.time() - audio_start
            
            task_logger.log_performance("音频轨道处理", audio_duration, 
                                       f"处理了 {len(audio_streams)} 个音频轨道")
            task_logger.complete_step("处理音频轨道信息", f"处理了 {len(audio_streams)} 个音频轨道")

            # Analyze bitrate statistics and integrate into basic info
            task_logger.start_step("分析比特率统计")
            try:
                bitrate_start = time.time()
                bitrate_stats = self.analyze_video_bitrate_stats(
                    video_id=video_id,
                    stream_type="video",
                    aggregation="time",
                    chunk_size=30.0
                )
                bitrate_duration = time.time() - bitrate_start
                
                task_logger.log_performance("比特率统计分析", bitrate_duration, 
                                           f"平均比特率: {bitrate_stats.avg_bitrate:.0f} bps")

                # Add bitrate stats to metadata
                metadata["bitrate_stats"] = {
                    "avg_fps": bitrate_stats.avg_fps,
                    "num_frames": bitrate_stats.num_frames,
                    "avg_bitrate": bitrate_stats.avg_bitrate,
                    "avg_bitrate_over_chunks": bitrate_stats.avg_bitrate_over_chunks,
                    "max_bitrate": bitrate_stats.max_bitrate,
                    "min_bitrate": bitrate_stats.min_bitrate,
                    "max_bitrate_factor": bitrate_stats.max_bitrate_factor,
                    "duration": bitrate_stats.duration,
                    "bitrate_per_chunk": bitrate_stats.bitrate_per_chunk,
                    "plot_data": bitrate_stats.plot_data
                }
                task_logger.complete_step("分析比特率统计", "比特率统计分析完成")

            except Exception as e:
                task_logger.log_warning("比特率统计分析失败", e)
                task_logger.complete_step("分析比特率统计", "比特率分析失败，跳过此步骤")

            # Commit changes
            task_logger.start_step("保存到数据库")
            commit_start = time.time()
            self.db.commit()
            commit_duration = time.time() - commit_start
            
            task_logger.log_performance("数据库提交", commit_duration, "保存所有更改到数据库")
            task_logger.complete_step("保存到数据库", "数据已成功保存")

            # 记录最终结果摘要
            task_logger.log_info("元数据分析结果摘要", {
                "duration": f"{metadata.get('duration', 'N/A')}s",
                "resolution": f"{metadata.get('width', 'N/A')}x{metadata.get('height', 'N/A')}",
                "fps": metadata.get("fps"),
                "codec": metadata.get("codec_name"),
                "bitrate": f"{metadata.get('bit_rate', 'N/A')} bps",
                "audio_streams": len(audio_streams)
            })

            task_logger.complete_task(True, "视频元数据分析成功完成")
            return metadata
            
        except Exception as e:
            self.db.rollback()
            task_logger.log_error("视频元数据分析失败", e)
            task_logger.complete_task(False, f"元数据分析失败: {str(e)}")
            raise
    
    def _update_video_basic_info(self, video: Video, metadata: Dict):
        """Update video basic information from metadata"""
        format_info = metadata.get("format", {})
        video_streams = metadata.get("video_streams", [])
        
        # Update from format information
        if format_info:
            video.duration = format_info.get("duration")
            video.bitrate = format_info.get("bit_rate")
        
        # Update from first video stream
        if video_streams:
            video_stream = video_streams[0]
            video.resolution = f"{video_stream.get('width')}x{video_stream.get('height')}"
            video.fps = video_stream.get("fps")
            video.codec = video_stream.get("codec_name")
    
    def _process_audio_tracks(self, video: Video, audio_streams: List[Dict]):
        """Process and store audio track information"""
        # Remove existing audio tracks
        self.db.query(AudioTrack).filter(AudioTrack.video_id == video.id).delete()
        
        # Add new audio tracks
        for stream in audio_streams:
            audio_track = AudioTrack(
                video_id=video.id,
                stream_index=stream.get("index"),
                codec_name=stream.get("codec_name"),
                codec_long_name=stream.get("codec_long_name"),
                sample_rate=stream.get("sample_rate"),
                channels=stream.get("channels"),
                channel_layout=stream.get("channel_layout"),
                bit_rate=stream.get("bit_rate"),
                duration=stream.get("duration"),
                tags=stream.get("tags", {})
            )
            self.db.add(audio_track)
    
    
    def extract_video_frames(self, video_id: int) -> List[str]:
        """Extract all frames from video using organized directory structure"""
        from app.tasks.task_logger import TaskLogger
        import time

        task_logger = TaskLogger("FRAME_EXTRACTION", video_id=video_id)
        task_logger.start_task(description="提取视频帧")

        video = self.db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise ValueError(f"Video {video_id} not found")

        task_logger.log_info("开始帧提取", {
            "video_file": os.path.basename(video.file_path),
            "duration": f"{video.duration}s" if video.duration else "N/A"
        })

        # Create organized directory structure
        task_logger.start_step("创建目录结构")
        dir_start = time.time()
        file_organizer.create_video_directory_structure(video_id)
        dir_duration = time.time() - dir_start

        task_logger.log_performance("目录结构创建", dir_duration, "创建视频文件组织结构")
        task_logger.complete_step("创建目录结构", "目录结构已创建")

        try:
            # Extract all frames using ffmpeg
            task_logger.start_step("提取视频帧")
            extract_start = time.time()
            all_frame_files = ffmpeg_service.extract_all_frames(video.file_path, video_id)
            extract_duration = time.time() - extract_start

            task_logger.log_performance("ffmpeg帧提取", extract_duration,
                                       f"提取了 {len(all_frame_files)} 个帧")
            task_logger.complete_step("提取视频帧", f"成功提取 {len(all_frame_files)} 个帧")

            # Remove existing frame records
            task_logger.start_step("清理旧记录")
            cleanup_start = time.time()
            deleted_count = self.db.query(VideoFrame).filter(VideoFrame.video_id == video_id).count()
            self.db.query(VideoFrame).filter(VideoFrame.video_id == video_id).delete()
            cleanup_duration = time.time() - cleanup_start
            
            task_logger.log_performance("数据库清理", cleanup_duration, f"删除了 {deleted_count} 条旧记录")
            task_logger.complete_step("清理旧记录", f"清理了 {deleted_count} 条旧记录")

            # Store frame information in database
            task_logger.start_step("保存帧信息到数据库")
            save_start = time.time()
            total_size = 0

            for i, (frame_path, timestamp, width, height) in enumerate(all_frame_files):
                # Get frame file info
                file_size = os.path.getsize(frame_path) if os.path.exists(frame_path) else 0
                total_size += file_size

                video_frame = VideoFrame(
                    video_id=video_id,
                    frame_number=i + 1,
                    timestamp=timestamp,
                    file_path=frame_path,
                    width=width,
                    height=height,
                    file_size=file_size,
                    is_key_frame=False  # Changed to False since we're extracting all frames
                )
                self.db.add(video_frame)

            save_duration = time.time() - save_start
            task_logger.log_performance("帧信息保存", save_duration,
                                       f"保存了 {len(all_frame_files)} 条帧记录")

            # Commit changes
            commit_start = time.time()
            self.db.commit()
            commit_duration = time.time() - commit_start

            task_logger.log_performance("数据库提交", commit_duration, "提交帧信息到数据库")
            task_logger.complete_step("保存帧信息到数据库", f"保存了 {len(all_frame_files)} 条记录")

            # Set the first frame as thumbnail if no thumbnail is set
            task_logger.start_step("设置缩略图")
            if all_frame_files and not video.key_frame_thumbnail_id:
                first_frame = self.db.query(VideoFrame).filter(
                    VideoFrame.video_id == video_id
                ).order_by(VideoFrame.timestamp).first()
                if first_frame:
                    video.key_frame_thumbnail_id = first_frame.id
                    self.db.commit()
                    task_logger.complete_step("设置缩略图", f"设置第一帧为缩略图 (ID: {first_frame.id})")
                else:
                    task_logger.complete_step("设置缩略图", "未找到可用的第一帧")
            else:
                task_logger.complete_step("设置缩略图", "缩略图已存在或无帧")

            # 记录最终统计信息
            avg_frame_size = total_size / len(all_frame_files) if all_frame_files else 0
            task_logger.log_info("帧提取完成统计", {
                "total_frames": len(all_frame_files),
                "total_size_mb": f"{total_size / 1024 / 1024:.2f}",
                "avg_frame_size_kb": f"{avg_frame_size / 1024:.2f}",
                "first_frame_timestamp": f"{all_frame_files[0][1]:.2f}s" if all_frame_files else "N/A",
                "last_frame_timestamp": f"{all_frame_files[-1][1]:.2f}s" if all_frame_files else "N/A"
            })

            task_logger.complete_task(True, f"成功提取 {len(all_frame_files)} 个帧")
            return [frame_path for frame_path, _, _, _ in all_frame_files]

        except Exception as e:
            self.db.rollback()
            task_logger.log_error("帧提取失败", e)
            task_logger.complete_task(False, f"帧提取失败: {str(e)}")
            raise

    def extract_video_key_frames(self, video_id: int) -> List[str]:
        """Extract key frames from video (deprecated - use extract_video_frames)"""
        logger.warning("extract_video_key_frames is deprecated, using extract_video_frames instead")
        return self.extract_video_frames(video_id)
    

    
    def get_video_analysis_summary(self, video_id: int) -> Dict:
        """Get comprehensive analysis summary for a video"""
        video = self.db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise ValueError(f"Video {video_id} not found")

        # Get related data
        audio_tracks = self.db.query(AudioTrack).filter(AudioTrack.video_id == video_id).all()
        frames = self.db.query(VideoFrame).filter(VideoFrame.video_id == video_id).all()

        return {
            "video_info": {
                "id": video.id,
                "filename": video.filename,
                "duration": video.duration,
                "resolution": video.resolution,
                "fps": video.fps,
                "codec": video.codec,
                "bitrate": video.bitrate,
                "file_size": video.file_size
            },
            "audio_tracks": [
                {
                    "id": track.id,
                    "stream_index": track.stream_index,
                    "codec_name": track.codec_name,
                    "sample_rate": track.sample_rate,
                    "channels": track.channels,
                    "duration": track.duration,
                    "file_path": track.file_path
                }
                for track in audio_tracks
            ],
            "frames": {
                "total_count": len(frames),
                "extraction_fps": frames[0].extraction_fps if frames else None,
                "frame_files": [frame.file_path for frame in frames]
            }
        }

    def generate_comprehensive_video_info(self, video_id: int) -> str:
        """
        Generate comprehensive video information and save as JSON file
        Returns the path to the generated JSON file
        """
        video = self.db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise ValueError(f"Video {video_id} not found")

        if not os.path.exists(video.file_path):
            raise FileNotFoundError(f"Video file not found: {video.file_path}")

        try:
            # Get comprehensive video information
            logger.info(f"Starting comprehensive analysis for video {video_id}")
            comprehensive_data = ffmpeg_service.get_comprehensive_video_info(video.file_path)

            # Generate output filename based on video filename
            video_filename = os.path.splitext(video.filename)[0]
            json_filename = f"{video_filename}.json"

            # Save to video's frames directory
            frames_dir = file_organizer.get_video_subdirectory(video_id, 'frames')
            json_file_path = frames_dir / json_filename

            # Write comprehensive data to JSON file
            with open(json_file_path, 'w', encoding='utf-8') as f:
                json.dump(comprehensive_data, f, indent=4, ensure_ascii=False)

            logger.info(f"Comprehensive video info saved to {json_file_path}")

            # Log file size for reference
            file_size = os.path.getsize(json_file_path)
            logger.info(f"Generated JSON file size: {file_size / (1024*1024):.2f} MB")

            return str(json_file_path)

        except Exception as e:
            logger.error(f"Failed to generate comprehensive video info for video {video_id}: {e}")
            raise

    def analyze_video_bitrate_stats(
        self,
        video_id: int,
        stream_type: str = "video",
        aggregation: str = "time",
        chunk_size: float = 30.0,
        plot_width: int = 70,
        plot_height: int = 18
    ) -> BitrateStats:
        """
        分析视频比特率统计并存储到数据库

        Args:
            video_id: 视频ID
            stream_type: 流类型 ("video" 或 "audio")
            aggregation: 聚合方式 ("time" 或 "gop")
            chunk_size: 块大小（秒，仅当aggregation="time"时有效）
            plot_width: 图表宽度
            plot_height: 图表高度

        Returns:
            BitrateStats: 比特率统计对象
        """
        try:
            logger.info(f"Starting bitrate statistics analysis for video {video_id}")

            # 创建比特率统计服务
            bitrate_service = BitrateStatsService(self.db)

            # 执行比特率分析
            bitrate_stats = bitrate_service.analyze_video_bitrate(
                video_id=video_id,
                stream_type=stream_type,
                aggregation=aggregation,
                chunk_size=chunk_size,
                plot_width=plot_width,
                plot_height=plot_height
            )

            logger.info(f"Bitrate statistics analysis completed for video {video_id}")
            return bitrate_stats

        except Exception as e:
            logger.error(f"Failed to analyze bitrate statistics for video {video_id}: {e}")
            raise

    def get_bitrate_stats_summary(self, video_id: int) -> Optional[Dict]:
        """获取视频比特率统计摘要"""
        try:
            bitrate_service = BitrateStatsService(self.db)
            return bitrate_service.get_bitrate_stats_summary(video_id)
        except Exception as e:
            logger.error(f"Failed to get bitrate stats summary for video {video_id}: {e}")
            return None
