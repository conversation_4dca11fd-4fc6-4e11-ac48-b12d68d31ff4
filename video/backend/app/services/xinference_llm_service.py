"""
Xinference LLM 服务
提供大语言模型相关功能
"""

from typing import Dict, List, Optional, Any, AsyncGenerator
from loguru import logger
import json

from app.core.config import settings
from app.core.xinference_config import ModelType, get_default_model_name
from app.services.xinference_client_service import xinference_client


class XinferenceLLMService:
    """Xinference LLM 服务"""
    
    def __init__(self):
        self.default_model = get_default_model_name(ModelType.LLM)
    
    async def chat(
        self,
        messages: List[Dict[str, str]],
        model_name: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        LLM 对话
        
        Args:
            messages: 对话消息列表
            model_name: 模型名称，默认使用配置的默认模型
            **kwargs: 其他生成参数
            
        Returns:
            对话响应结果
        """
        try:
            model_name = model_name or self.default_model
            
            # 确保模型可用
            model_uid = await xinference_client.ensure_model_available(
                model_name, ModelType.LLM
            )
            
            if not model_uid:
                raise RuntimeError(f"Failed to launch LLM model: {model_name}")
            
            # 构建请求参数
            request_data = {
                "model": model_uid,
                "messages": messages,
                **kwargs
            }
            
            # 发送对话请求
            response = await xinference_client._make_request(
                "POST",
                "/v1/chat/completions",
                json=request_data
            )
            
            logger.info(f"LLM chat completed for model {model_name}")
            return response
            
        except Exception as e:
            logger.error(f"LLM chat failed: {e}")
            raise
    
    async def generate(
        self,
        prompt: str,
        model_name: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        文本生成
        
        Args:
            prompt: 输入提示
            model_name: 模型名称
            **kwargs: 其他生成参数
            
        Returns:
            生成结果
        """
        try:
            model_name = model_name or self.default_model
            
            # 确保模型可用
            model_uid = await xinference_client.ensure_model_available(
                model_name, ModelType.LLM
            )
            
            if not model_uid:
                raise RuntimeError(f"Failed to launch LLM model: {model_name}")
            
            # 构建请求参数
            request_data = {
                "model": model_uid,
                "prompt": prompt,
                **kwargs
            }
            
            # 发送生成请求
            response = await xinference_client._make_request(
                "POST",
                "/v1/completions",
                json=request_data
            )
            
            logger.info(f"LLM generation completed for model {model_name}")
            return response
            
        except Exception as e:
            logger.error(f"LLM generation failed: {e}")
            raise
    
    async def stream_chat(
        self,
        messages: List[Dict[str, str]],
        model_name: Optional[str] = None,
        **kwargs
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        流式对话
        
        Args:
            messages: 对话消息列表
            model_name: 模型名称
            **kwargs: 其他生成参数
            
        Yields:
            流式响应数据
        """
        try:
            model_name = model_name or self.default_model
            
            # 确保模型可用
            model_uid = await xinference_client.ensure_model_available(
                model_name, ModelType.LLM
            )
            
            if not model_uid:
                raise RuntimeError(f"Failed to launch LLM model: {model_name}")
            
            # 构建请求参数
            request_data = {
                "model": model_uid,
                "messages": messages,
                "stream": True,
                **kwargs
            }
            
            # 发送流式请求
            client = await xinference_client._get_client()
            async with client.stream(
                "POST",
                "/v1/chat/completions",
                json=request_data
            ) as response:
                response.raise_for_status()
                
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        data = line[6:]  # 移除 "data: " 前缀
                        if data.strip() == "[DONE]":
                            break
                        try:
                            chunk = json.loads(data)
                            yield chunk
                        except json.JSONDecodeError:
                            continue
            
            logger.info(f"LLM stream chat completed for model {model_name}")
            
        except Exception as e:
            logger.error(f"LLM stream chat failed: {e}")
            raise
    
    async def analyze_video_frame(
        self,
        frame_path: str,
        question: str = "请详细描述这张图片的内容",
        model_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        分析视频帧
        
        Args:
            frame_path: 帧图片路径
            question: 分析问题
            model_name: 模型名称
            
        Returns:
            分析结果
        """
        try:
            # 构建多模态消息
            messages = [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": question},
                        {"type": "image_url", "image_url": {"url": frame_path}}
                    ]
                }
            ]
            
            response = await self.chat(messages, model_name)
            
            return {
                "frame_path": frame_path,
                "question": question,
                "analysis": response.get("choices", [{}])[0].get("message", {}).get("content", ""),
                "model": model_name or self.default_model,
                "usage": response.get("usage", {})
            }
            
        except Exception as e:
            logger.error(f"Video frame analysis failed: {e}")
            raise
    
    async def generate_summary(
        self,
        content: str,
        summary_type: str = "general",
        model_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        生成内容摘要
        
        Args:
            content: 要总结的内容
            summary_type: 摘要类型 (general, key_points, brief)
            model_name: 模型名称
            
        Returns:
            摘要结果
        """
        try:
            # 根据摘要类型构建提示
            prompts = {
                "general": "请对以下内容进行详细总结：\n\n{content}",
                "key_points": "请提取以下内容的关键要点：\n\n{content}",
                "brief": "请用一句话简要概括以下内容：\n\n{content}"
            }
            
            prompt = prompts.get(summary_type, prompts["general"]).format(content=content)
            
            messages = [{"role": "user", "content": prompt}]
            response = await self.chat(messages, model_name)
            
            return {
                "original_content": content,
                "summary_type": summary_type,
                "summary": response.get("choices", [{}])[0].get("message", {}).get("content", ""),
                "model": model_name or self.default_model,
                "usage": response.get("usage", {})
            }
            
        except Exception as e:
            logger.error(f"Summary generation failed: {e}")
            raise
    
    async def extract_keywords(
        self,
        text: str,
        max_keywords: int = 10,
        model_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        提取关键词
        
        Args:
            text: 输入文本
            max_keywords: 最大关键词数量
            model_name: 模型名称
            
        Returns:
            关键词提取结果
        """
        try:
            prompt = f"""请从以下文本中提取最重要的{max_keywords}个关键词，以JSON格式返回：
            
文本：{text}

请返回格式：
{{"keywords": ["关键词1", "关键词2", ...]}}"""
            
            messages = [{"role": "user", "content": prompt}]
            response = await self.chat(messages, model_name)
            
            content = response.get("choices", [{}])[0].get("message", {}).get("content", "")
            
            # 尝试解析JSON
            try:
                keywords_data = json.loads(content)
                keywords = keywords_data.get("keywords", [])
            except json.JSONDecodeError:
                # 如果JSON解析失败，尝试简单的文本解析
                keywords = [kw.strip() for kw in content.split(",")][:max_keywords]
            
            return {
                "text": text,
                "keywords": keywords,
                "model": model_name or self.default_model,
                "usage": response.get("usage", {})
            }
            
        except Exception as e:
            logger.error(f"Keyword extraction failed: {e}")
            raise


# 全局 LLM 服务实例
xinference_llm_service = XinferenceLLMService()
