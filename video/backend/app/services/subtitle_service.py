"""
Subtitle processing service for video analysis
"""

import os
import json
import time
import asyncio
from typing import Dict, List, Optional
from sqlalchemy.orm import Session
from loguru import logger

from app.models.task import Video, AudioTrack, Subtitle
from app.core.config import settings


class SubtitleService:
    """Service for subtitle processing and management"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def upload_manual_subtitle(self, video_id: int, srt_file_path: str, language: str = "zh-cn") -> Subtitle:
        """Upload and process manual SRT subtitle file"""
        video = self.db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise ValueError(f"Video {video_id} not found")
        
        if not os.path.exists(srt_file_path):
            raise FileNotFoundError(f"SRT file not found: {srt_file_path}")
        
        try:
            # Parse SRT content
            subtitle_content = self._parse_srt_file(srt_file_path)
            
            # Create subtitle record
            subtitle = Subtitle(
                video_id=video_id,
                subtitle_type="manual",
                language=language,
                file_path=srt_file_path,
                content=json.dumps(subtitle_content),
                confidence=1.0  # Manual subtitles have 100% confidence
            )
            
            self.db.add(subtitle)
            self.db.commit()
            
            logger.info(f"Successfully uploaded manual subtitle for video {video_id}")
            return subtitle
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to upload manual subtitle for video {video_id}: {e}")
            raise
    
    def generate_automatic_subtitle(self, video_id: int, audio_track_id: Optional[int] = None) -> Subtitle:
        """Generate automatic subtitle from audio content"""
        from app.tasks.task_logger import TaskLogger
        import time

        task_logger = TaskLogger("SUBTITLE_SERVICE", video_id=video_id)
        task_logger.start_task(description="自动字幕生成服务")

        # 检查是否使用 Xinference 服务
        if self._should_use_xinference():
            task_logger.log_info("使用 Xinference 音频服务生成字幕")
            return self._generate_subtitle_with_xinference(video_id, audio_track_id)
        else:
            task_logger.log_info("使用本地音频服务生成字幕")
            return self._generate_subtitle_with_local_service(video_id, audio_track_id)

    def _should_use_xinference(self) -> bool:
        """判断是否应该使用 Xinference 服务"""
        return settings.XINFERENCE_ENABLED

    def _generate_subtitle_with_xinference(self, video_id: int, audio_track_id: Optional[int] = None) -> Subtitle:
        """使用 Xinference 服务生成字幕"""
        from app.services.xinference_subtitle_service import get_xinference_subtitle_service

        xinference_service = get_xinference_subtitle_service(self.db)

        # 运行异步方法
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(
                xinference_service.generate_automatic_subtitle(video_id, audio_track_id)
            )
        finally:
            loop.close()

    def _generate_subtitle_with_local_service(self, video_id: int, audio_track_id: Optional[int] = None) -> Subtitle:
        """使用本地服务生成字幕"""
        from app.tasks.task_logger import TaskLogger
        import time

        task_logger = TaskLogger("SUBTITLE_SERVICE", video_id=video_id)
        task_logger.start_task(description="本地自动字幕生成服务")

        video = self.db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise ValueError(f"Video {video_id} not found")

        # Get audio track to process
        task_logger.start_step("获取音频轨道")
        if audio_track_id:
            audio_track = self.db.query(AudioTrack).filter(
                AudioTrack.id == audio_track_id,
                AudioTrack.video_id == video_id
            ).first()
            task_logger.log_info(f"使用指定音频轨道 ID: {audio_track_id}")
        else:
            # Use first audio track if not specified
            audio_track = self.db.query(AudioTrack).filter(
                AudioTrack.video_id == video_id
            ).first()
            task_logger.log_info("使用第一个可用音频轨道")

        if not audio_track:
            raise ValueError(f"No audio track found for video {video_id}")

        if not audio_track.file_path or not os.path.exists(audio_track.file_path):
            raise FileNotFoundError(f"Audio file not found: {audio_track.file_path}. Please extract audio first.")

        # 记录音频轨道信息
        audio_file_size = os.path.getsize(audio_track.file_path)
        task_logger.log_info("音频轨道详情", {
            "audio_file": os.path.basename(audio_track.file_path),
            "file_size_mb": f"{audio_file_size / 1024 / 1024:.2f}",
            "codec": audio_track.codec_name,
            "sample_rate": audio_track.sample_rate,
            "channels": audio_track.channels,
            "duration": f"{audio_track.duration}s" if audio_track.duration else "N/A"
        })
        task_logger.complete_step("获取音频轨道", f"音频文件: {os.path.basename(audio_track.file_path)}")

        try:
            start_time = time.time()

            # Use the existing audio parsing functionality
            task_logger.start_step("语音识别处理")
            recognition_start = time.time()
            subtitle_content = self._generate_subtitle_from_audio(audio_track.file_path)
            recognition_duration = time.time() - recognition_start

            task_logger.log_performance("语音识别", recognition_duration,
                                       f"识别出 {len(subtitle_content)} 个字幕段落")
            task_logger.complete_step("语音识别处理", f"生成了 {len(subtitle_content)} 个字幕段落")

            processing_time = time.time() - start_time

            # Calculate average confidence
            task_logger.start_step("计算置信度")
            confidence_start = time.time()
            avg_confidence = self._calculate_average_confidence(subtitle_content)
            confidence_duration = time.time() - confidence_start

            task_logger.log_performance("置信度计算", confidence_duration, f"平均置信度: {avg_confidence:.2f}")
            task_logger.complete_step("计算置信度", f"平均置信度: {avg_confidence:.2f}")

            # Generate SRT file and save to subtitles directory
            task_logger.start_step("生成并保存SRT文件")
            from app.utils.file_organization import file_organizer
            srt_file_path = file_organizer.get_subtitle_file_path(video_id, "zh-cn")

            # Convert subtitle content to SRT format and save
            srt_start = time.time()
            srt_content = self._convert_to_srt_format(subtitle_content)
            with open(srt_file_path, 'w', encoding='utf-8') as f:
                f.write(srt_content)
            srt_duration = time.time() - srt_start

            # 获取SRT文件大小
            srt_file_size = os.path.getsize(srt_file_path)
            task_logger.log_performance("SRT文件生成", srt_duration,
                                       f"文件大小: {srt_file_size / 1024:.2f} KB")
            task_logger.complete_step("生成并保存SRT文件", f"SRT文件: {os.path.basename(srt_file_path)}")

            # Create subtitle record
            task_logger.start_step("保存字幕记录")
            db_start = time.time()
            subtitle = Subtitle(
                video_id=video_id,
                audio_track_id=audio_track_id,
                subtitle_type="auto_generated",
                language="zh-cn",  # Default to Chinese
                file_path=str(srt_file_path),  # Save the SRT file path
                content=json.dumps(subtitle_content),
                confidence=avg_confidence,
                processing_time=processing_time
            )

            self.db.add(subtitle)
            self.db.commit()
            self.db.refresh(subtitle)
            db_duration = time.time() - db_start

            task_logger.log_performance("数据库保存", db_duration, f"字幕记录ID: {subtitle.id}")
            task_logger.complete_step("保存字幕记录", f"记录ID: {subtitle.id}")

            # 记录最终统计信息
            task_logger.log_info("字幕生成完成统计", {
                "subtitle_id": subtitle.id,
                "segments_count": len(subtitle_content),
                "confidence": f"{avg_confidence:.2f}",
                "total_processing_time": f"{processing_time:.2f}s",
                "srt_file_size_kb": f"{srt_file_size / 1024:.2f}",
                "language": "zh-cn",
                "srt_file_path": str(srt_file_path)
            })

            task_logger.complete_task(True, f"本地自动字幕生成成功，ID: {subtitle.id}，置信度: {avg_confidence:.2f}")
            return subtitle

        except Exception as e:
            self.db.rollback()
            task_logger.log_error("本地自动字幕生成失败", e)
            task_logger.complete_task(False, f"字幕生成失败: {str(e)}")
            raise
    
    def _parse_srt_file(self, srt_file_path: str) -> List[Dict]:
        """Parse SRT file into structured format"""
        subtitle_entries = []
        
        with open(srt_file_path, 'r', encoding='utf-8') as file:
            content = file.read().strip()
        
        # Split by double newlines to get individual subtitle blocks
        blocks = content.split('\n\n')
        
        for block in blocks:
            lines = block.strip().split('\n')
            if len(lines) >= 3:
                try:
                    # Parse subtitle number
                    subtitle_num = int(lines[0])
                    
                    # Parse time range
                    time_line = lines[1]
                    start_time, end_time = time_line.split(' --> ')
                    
                    # Parse text (may be multiple lines)
                    text = '\n'.join(lines[2:])
                    
                    subtitle_entries.append({
                        "index": subtitle_num,
                        "start_time": self._parse_srt_time(start_time),
                        "end_time": self._parse_srt_time(end_time),
                        "text": text,
                        "confidence": 1.0
                    })
                    
                except (ValueError, IndexError) as e:
                    logger.warning(f"Failed to parse subtitle block: {block[:50]}... Error: {e}")
                    continue
        
        return subtitle_entries
    
    def _parse_srt_time(self, time_str: str) -> float:
        """Parse SRT time format (HH:MM:SS,mmm) to seconds"""
        try:
            time_part, ms_part = time_str.split(',')
            hours, minutes, seconds = map(int, time_part.split(':'))
            milliseconds = int(ms_part)
            
            total_seconds = hours * 3600 + minutes * 60 + seconds + milliseconds / 1000.0
            return total_seconds
        except (ValueError, IndexError):
            return 0.0
    
    def _generate_subtitle_from_audio(self, audio_file_path: str) -> List[Dict]:
        """
        Generate subtitle from audio using the local audio processing service
        """
        try:
            from app.services.local_audio_service import local_audio_service

            # Check if audio file exists
            if not os.path.exists(audio_file_path):
                raise FileNotFoundError(f"Audio file not found: {audio_file_path}")

            # Check if local audio service is available
            if not local_audio_service.is_available():
                raise Exception("Audio processing service is not available. Please install required dependencies.")

            # Use local audio service to generate subtitles
            logger.info(f"Using local audio service to process: {audio_file_path}")
            subtitle_entries = local_audio_service.generate_subtitle_from_audio(audio_file_path)

            if not subtitle_entries:
                logger.warning("No subtitle content generated from audio")
                return []

            return subtitle_entries

        except Exception as e:
            logger.error(f"Failed to generate subtitle from audio: {e}")
            raise Exception(f"Subtitle generation failed: {str(e)}")
    

    
    def _calculate_average_confidence(self, subtitle_content: List[Dict]) -> float:
        """Calculate average confidence score from subtitle entries"""
        if not subtitle_content:
            return 0.0
        
        total_confidence = sum(entry.get('confidence', 0.0) for entry in subtitle_content)
        return total_confidence / len(subtitle_content)
    
    def export_subtitle_to_srt(self, subtitle_id: int, output_path: str) -> str:
        """Export subtitle to SRT format"""
        subtitle = self.db.query(Subtitle).filter(Subtitle.id == subtitle_id).first()
        if not subtitle:
            raise ValueError(f"Subtitle {subtitle_id} not found")
        
        try:
            subtitle_content = json.loads(subtitle.content)
            srt_content = self._convert_to_srt_format(subtitle_content)
            
            with open(output_path, 'w', encoding='utf-8') as file:
                file.write(srt_content)
            
            logger.info(f"Exported subtitle {subtitle_id} to {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"Failed to export subtitle {subtitle_id}: {e}")
            raise
    
    def _convert_to_srt_format(self, subtitle_content: List[Dict]) -> str:
        """Convert subtitle content to SRT format"""
        srt_lines = []
        
        for entry in subtitle_content:
            index = entry.get('index', 1)
            start_time = self._seconds_to_srt_time(entry.get('start_time', 0.0))
            end_time = self._seconds_to_srt_time(entry.get('end_time', 0.0))
            text = entry.get('text', '')
            
            srt_lines.append(f"{index}")
            srt_lines.append(f"{start_time} --> {end_time}")
            srt_lines.append(text)
            srt_lines.append("")  # Empty line between entries
        
        return '\n'.join(srt_lines)
    
    def _seconds_to_srt_time(self, seconds: float) -> str:
        """Convert seconds to SRT time format (HH:MM:SS,mmm)"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        milliseconds = int((seconds % 1) * 1000)
        
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{milliseconds:03d}"
