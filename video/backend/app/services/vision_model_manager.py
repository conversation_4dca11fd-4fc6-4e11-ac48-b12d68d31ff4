"""
视觉模型管理器
统一管理所有视觉理解模型，提供统一的调用接口
"""

import asyncio
from typing import Dict, List, Any, Optional
from enum import Enum
import logging

from app.services.vision_model_interface import VisionModelInterface, VisionModelError
from app.core.config import settings

logger = logging.getLogger(__name__)


class ModelProvider(Enum):
    """模型提供商枚举"""
    SILICONFLOW = "siliconflow"
    ZHIPU = "zhipu"
    VOLCENGINE = "volcengine"
    MINICPM = "minicpm"


class VisionModelManager:
    """视觉模型管理器"""
    
    def __init__(self):
        self._models: Dict[str, VisionModelInterface] = {}
        self._initialized = False
    
    async def initialize(self):
        """初始化所有可用的模型"""
        if self._initialized:
            return
        
        logger.info("Initializing vision model manager...")
        
        # 动态加载和初始化模型
        await self._load_models()
        
        self._initialized = True
        logger.info(f"Vision model manager initialized with {len(self._models)} models")
    
    async def _load_models(self):
        """加载所有可用的模型"""
        model_configs = [
            {
                "provider": ModelProvider.SILICONFLOW,
                "enabled": bool(settings.SILICONFLOW_API_KEY if hasattr(settings, 'SILICONFLOW_API_KEY') else False),
                "class_path": "app.services.siliconflow_vision_service.SiliconFlowVisionService"
            },
            {
                "provider": ModelProvider.ZHIPU,
                "enabled": bool(settings.ZHIPU_API_KEY if hasattr(settings, 'ZHIPU_API_KEY') else False),
                "class_path": "app.services.zhipu_vision_service.ZhipuVisionService"
            },
            {
                "provider": ModelProvider.VOLCENGINE,
                "enabled": bool(settings.VOLCENGINE_API_KEY if hasattr(settings, 'VOLCENGINE_API_KEY') else False),
                "class_path": "app.services.volcengine_vision_service.VolcengineVisionService"
            },
            {
                "provider": ModelProvider.MINICPM,
                "enabled": True,  # MiniCPM 是本地模型，默认启用
                "class_path": "app.services.minicpm_v4_service.MiniCPMV4Service"
            }
        ]
        
        for config in model_configs:
            if config["enabled"]:
                try:
                    await self._load_single_model(config)
                except Exception as e:
                    logger.error(f"Failed to load {config['provider'].value} model: {e}")
    
    async def _load_single_model(self, config: Dict[str, Any]):
        """加载单个模型"""
        provider = config["provider"]
        class_path = config["class_path"]
        
        try:
            # 动态导入模型类
            module_path, class_name = class_path.rsplit('.', 1)
            module = __import__(module_path, fromlist=[class_name])
            model_class = getattr(module, class_name)
            
            # 创建模型实例
            if provider == ModelProvider.MINICPM:
                # MiniCPM 需要数据库连接
                from app.core.database import SessionLocal
                db = SessionLocal()
                model_instance = model_class(db)
            else:
                model_instance = model_class()
            
            # 初始化模型
            if await model_instance.initialize():
                self._models[provider.value] = model_instance
                logger.info(f"Successfully loaded {provider.value} model")
            else:
                logger.warning(f"Failed to initialize {provider.value} model")
                
        except Exception as e:
            logger.error(f"Error loading {provider.value} model: {e}")
            raise
    
    def get_available_models(self) -> List[str]:
        """获取可用的模型列表"""
        return list(self._models.keys())
    
    def get_model(self, model_name: str) -> Optional[VisionModelInterface]:
        """获取指定的模型实例"""
        return self._models.get(model_name)
    
    async def analyze_single_image(
        self, 
        image_path: str, 
        question: str = "请详细描述这张图片的内容",
        model_name: str = None,
        **kwargs
    ) -> Dict[str, Any]:
        """使用指定模型分析单张图片"""
        if not self._initialized:
            await self.initialize()
        
        model = self._get_model_or_default(model_name)
        if not model:
            raise VisionModelError("No available vision model found")
        
        try:
            result = await model.analyze_single_image(image_path, question, **kwargs)
            return {
                "success": True,
                "model_used": model.model_name,
                "result": result
            }
        except Exception as e:
            logger.error(f"Error analyzing single image with {model.model_name}: {e}")
            return {
                "success": False,
                "model_used": model.model_name,
                "error": str(e)
            }
    
    async def analyze_multiple_images(
        self, 
        image_paths: List[str], 
        question: str = "请分析这些图片的内容和关联性",
        model_name: str = None,
        **kwargs
    ) -> Dict[str, Any]:
        """使用指定模型分析多张图片"""
        if not self._initialized:
            await self.initialize()
        
        model = self._get_model_or_default(model_name)
        if not model:
            raise VisionModelError("No available vision model found")
        
        try:
            result = await model.analyze_multiple_images(image_paths, question, **kwargs)
            return {
                "success": True,
                "model_used": model.model_name,
                "result": result
            }
        except Exception as e:
            logger.error(f"Error analyzing multiple images with {model.model_name}: {e}")
            return {
                "success": False,
                "model_used": model.model_name,
                "error": str(e)
            }
    
    async def compare_images(
        self, 
        image_paths: List[str], 
        question: str = "请比较这些图片的异同点",
        model_name: str = None,
        **kwargs
    ) -> Dict[str, Any]:
        """使用指定模型比较多张图片"""
        if not self._initialized:
            await self.initialize()
        
        model = self._get_model_or_default(model_name)
        if not model:
            raise VisionModelError("No available vision model found")
        
        try:
            result = await model.compare_images(image_paths, question, **kwargs)
            return {
                "success": True,
                "model_used": model.model_name,
                "result": result
            }
        except Exception as e:
            logger.error(f"Error comparing images with {model.model_name}: {e}")
            return {
                "success": False,
                "model_used": model.model_name,
                "error": str(e)
            }
    
    async def health_check_all(self) -> Dict[str, Any]:
        """检查所有模型的健康状态"""
        if not self._initialized:
            await self.initialize()
        
        results = {}
        for model_name, model in self._models.items():
            results[model_name] = await model.health_check()
        
        return {
            "total_models": len(self._models),
            "healthy_models": sum(1 for r in results.values() if r.get("status") == "healthy"),
            "models": results
        }
    
    def _get_model_or_default(self, model_name: str = None) -> Optional[VisionModelInterface]:
        """获取指定模型或默认模型"""
        if model_name and model_name in self._models:
            return self._models[model_name]
        
        # 返回第一个可用的模型作为默认模型
        if self._models:
            return next(iter(self._models.values()))
        
        return None
    
    def get_models_info(self) -> Dict[str, Any]:
        """获取所有模型的信息"""
        return {
            model_name: model.get_model_info() 
            for model_name, model in self._models.items()
        }


# 全局实例
vision_model_manager = VisionModelManager()
