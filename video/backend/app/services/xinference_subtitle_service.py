"""
Xinference 字幕服务
使用 Xinference Audio 模型生成字幕
"""

import os
import json
import time
import asyncio
from typing import Dict, List, Optional
from sqlalchemy.orm import Session
from loguru import logger

from app.models.task import Video, AudioTrack, Subtitle
from app.services.xinference_audio_service import XinferenceAudioService
from app.core.config import settings


class XinferenceSubtitleService:
    """使用 Xinference 的字幕服务"""
    
    def __init__(self, db: Session):
        self.db = db
        self.audio_service = XinferenceAudioService()
    
    def is_available(self) -> bool:
        """检查 Xinference 字幕服务是否可用"""
        return settings.XINFERENCE_ENABLED
    
    async def generate_automatic_subtitle(self, video_id: int, audio_track_id: Optional[int] = None) -> Subtitle:
        """使用 Xinference 生成自动字幕"""
        from app.tasks.task_logger import TaskLogger
        
        task_logger = TaskLogger("XINFERENCE_SUBTITLE_SERVICE", video_id=video_id)
        task_logger.start_task(description="Xinference 自动字幕生成服务")
        
        video = self.db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise ValueError(f"Video {video_id} not found")
        
        # 获取音频轨道
        if audio_track_id:
            audio_track = self.db.query(AudioTrack).filter(
                AudioTrack.id == audio_track_id,
                AudioTrack.video_id == video_id
            ).first()
        else:
            # 使用第一个音频轨道
            audio_track = self.db.query(AudioTrack).filter(
                AudioTrack.video_id == video_id
            ).first()
        
        if not audio_track:
            raise ValueError(f"No audio track found for video {video_id}")
        
        if not os.path.exists(audio_track.file_path):
            raise FileNotFoundError(f"Audio file not found: {audio_track.file_path}")
        
        try:
            start_time = time.time()
            
            # 使用 Xinference 进行语音识别
            task_logger.start_step("Xinference 语音识别处理")
            recognition_start = time.time()
            
            # 调用 Xinference Audio 服务
            transcription_result = await self.audio_service.transcribe_file(
                audio_track.file_path,
                language="zh"  # 中文
            )
            
            recognition_duration = time.time() - recognition_start
            
            # 转换为字幕格式
            subtitle_content = self._convert_transcription_to_subtitle(transcription_result)
            
            task_logger.log_performance("Xinference 语音识别", recognition_duration, 
                                       f"识别出 {len(subtitle_content)} 个字幕段落")
            task_logger.complete_step("Xinference 语音识别处理", f"生成了 {len(subtitle_content)} 个字幕段落")
            
            processing_time = time.time() - start_time
            
            # 计算平均置信度
            task_logger.start_step("计算置信度")
            confidence_start = time.time()
            avg_confidence = self._calculate_average_confidence(subtitle_content)
            confidence_duration = time.time() - confidence_start
            
            task_logger.log_performance("置信度计算", confidence_duration, f"平均置信度: {avg_confidence:.2f}")
            task_logger.complete_step("计算置信度", f"平均置信度: {avg_confidence:.2f}")
            
            # 生成并保存 SRT 文件
            task_logger.start_step("生成并保存SRT文件")
            from app.utils.file_organization import file_organizer
            srt_file_path = file_organizer.get_subtitle_file_path(video_id, "zh-cn")
            
            # 确保目录存在
            os.makedirs(os.path.dirname(srt_file_path), exist_ok=True)
            
            # 生成 SRT 内容
            srt_content = self._generate_srt_content(subtitle_content)
            
            # 保存 SRT 文件
            with open(srt_file_path, 'w', encoding='utf-8') as f:
                f.write(srt_content)
            
            task_logger.complete_step("生成并保存SRT文件", f"SRT文件已保存: {srt_file_path}")
            
            # 创建字幕记录
            task_logger.start_step("保存字幕记录")
            db_start = time.time()
            subtitle = Subtitle(
                video_id=video_id,
                audio_track_id=audio_track_id,
                subtitle_type="auto_generated_xinference",
                language="zh-cn",
                file_path=str(srt_file_path),
                content=json.dumps(subtitle_content),
                confidence=avg_confidence,
                processing_time=processing_time
            )
            
            self.db.add(subtitle)
            self.db.commit()
            self.db.refresh(subtitle)
            
            db_duration = time.time() - db_start
            task_logger.log_performance("数据库保存", db_duration, f"字幕ID: {subtitle.id}")
            task_logger.complete_step("保存字幕记录", f"字幕记录已保存，ID: {subtitle.id}")
            
            total_duration = time.time() - start_time
            task_logger.complete_task(f"字幕生成完成，总耗时: {total_duration:.2f}秒")
            
            return subtitle
            
        except Exception as e:
            task_logger.log_error("字幕生成失败", e)
            task_logger.complete_task("字幕生成失败")
            raise Exception(f"Xinference subtitle generation failed: {str(e)}")
    
    def _convert_transcription_to_subtitle(self, transcription_result: Dict) -> List[Dict]:
        """将 Xinference 转录结果转换为字幕格式"""
        subtitle_entries = []
        
        # 检查转录结果格式
        if "text" in transcription_result:
            # 简单文本格式，需要分段
            text = transcription_result["text"]
            segments = self._split_text_into_segments(text)
            
            # 为每个段落分配时间戳（简单平均分配）
            total_duration = transcription_result.get("duration", 60.0)  # 默认60秒
            segment_duration = total_duration / len(segments) if segments else 1.0
            
            for i, segment_text in enumerate(segments):
                start_time = i * segment_duration
                end_time = (i + 1) * segment_duration
                
                subtitle_entries.append({
                    "start": start_time,
                    "end": end_time,
                    "text": segment_text.strip(),
                    "confidence": 0.8  # 默认置信度
                })
        
        elif "segments" in transcription_result:
            # 带时间戳的段落格式
            for segment in transcription_result["segments"]:
                subtitle_entries.append({
                    "start": segment.get("start", 0.0),
                    "end": segment.get("end", 1.0),
                    "text": segment.get("text", "").strip(),
                    "confidence": segment.get("confidence", 0.8)
                })
        
        return subtitle_entries
    
    def _split_text_into_segments(self, text: str, max_length: int = 50) -> List[str]:
        """将长文本分割为适合字幕的段落"""
        import re
        
        # 按句号、问号、感叹号分割
        sentences = re.split(r'[。！？.!?]+', text)
        segments = []
        current_segment = ""
        
        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue
            
            # 如果当前段落加上新句子不超过最大长度，则合并
            if len(current_segment + sentence) <= max_length:
                current_segment += sentence
            else:
                # 保存当前段落，开始新段落
                if current_segment:
                    segments.append(current_segment)
                current_segment = sentence
        
        # 添加最后一个段落
        if current_segment:
            segments.append(current_segment)
        
        return segments
    
    def _calculate_average_confidence(self, subtitle_content: List[Dict]) -> float:
        """计算平均置信度"""
        if not subtitle_content:
            return 0.0
        
        total_confidence = sum(entry.get("confidence", 0.0) for entry in subtitle_content)
        return total_confidence / len(subtitle_content)
    
    def _generate_srt_content(self, subtitle_content: List[Dict]) -> str:
        """生成 SRT 格式内容"""
        srt_lines = []
        
        for i, entry in enumerate(subtitle_content, 1):
            start_time = self._format_srt_time(entry["start"])
            end_time = self._format_srt_time(entry["end"])
            text = entry["text"]
            
            srt_lines.append(f"{i}")
            srt_lines.append(f"{start_time} --> {end_time}")
            srt_lines.append(text)
            srt_lines.append("")  # 空行分隔
        
        return "\n".join(srt_lines)
    
    def _format_srt_time(self, seconds: float) -> str:
        """格式化 SRT 时间戳"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        milliseconds = int((seconds % 1) * 1000)
        
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{milliseconds:03d}"


# 全局服务实例
xinference_subtitle_service = None

def get_xinference_subtitle_service(db: Session) -> XinferenceSubtitleService:
    """获取 Xinference 字幕服务实例"""
    return XinferenceSubtitleService(db)
