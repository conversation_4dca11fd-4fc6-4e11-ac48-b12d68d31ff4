"""
Xinference API 端点
提供 Xinference 模型管理和调用的 API 接口
"""

from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
import json
import io
from loguru import logger

from app.core.database import get_db
from app.services.xinference_service import xinference_service
from app.core.xinference_config import ModelType


router = APIRouter()


# 模型管理端点
@router.get("/health")
async def health_check():
    """健康检查"""
    try:
        health_status = await xinference_service.health_check()
        return {
            "status": "success",
            "data": health_status
        }
    except Exception as e:
        logger.error(f"Xinference health check failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/models/available")
async def list_available_models():
    """列出所有可用模型"""
    try:
        models = await xinference_service.list_available_models()
        return {
            "status": "success",
            "data": models
        }
    except Exception as e:
        logger.error(f"Failed to list available models: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/models/running")
async def list_running_models():
    """列出正在运行的模型"""
    try:
        models = await xinference_service.get_running_models()
        return {
            "status": "success",
            "data": models
        }
    except Exception as e:
        logger.error(f"Failed to list running models: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/models/{model_name}/launch")
async def launch_model(model_name: str):
    """启动模型"""
    try:
        model_uid = await xinference_service.launch_model_by_name(model_name)
        if not model_uid:
            raise HTTPException(status_code=400, detail=f"Failed to launch model: {model_name}")
        
        return {
            "status": "success",
            "message": f"Model {model_name} launched successfully",
            "data": {
                "model_name": model_name,
                "model_uid": model_uid
            }
        }
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to launch model {model_name}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/models/{model_uid}")
async def terminate_model(model_uid: str):
    """停止模型"""
    try:
        success = await xinference_service.terminate_model_by_uid(model_uid)
        if not success:
            raise HTTPException(status_code=400, detail=f"Failed to terminate model: {model_uid}")
        
        return {
            "status": "success",
            "message": f"Model {model_uid} terminated successfully"
        }
    except Exception as e:
        logger.error(f"Failed to terminate model {model_uid}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/models/{model_uid}/status")
async def get_model_status(model_uid: str):
    """获取模型状态"""
    try:
        status = await xinference_service.get_model_info(model_uid)
        if not status:
            raise HTTPException(status_code=404, detail=f"Model not found: {model_uid}")
        
        return {
            "status": "success",
            "data": status
        }
    except Exception as e:
        logger.error(f"Failed to get model status {model_uid}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# LLM 端点
@router.post("/llm/chat")
async def llm_chat(
    messages: List[Dict[str, str]] = Form(...),
    model_name: Optional[str] = Form(None),
    max_tokens: Optional[int] = Form(1024),
    temperature: Optional[float] = Form(0.7)
):
    """LLM 对话"""
    try:
        # 解析 JSON 格式的 messages
        if isinstance(messages, str):
            messages = json.loads(messages)
        
        response = await xinference_service.chat(
            messages=messages,
            model_name=model_name,
            max_tokens=max_tokens,
            temperature=temperature
        )
        
        return {
            "status": "success",
            "data": response
        }
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Invalid JSON format for messages")
    except Exception as e:
        logger.error(f"LLM chat failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/llm/generate")
async def llm_generate(
    prompt: str = Form(...),
    model_name: Optional[str] = Form(None),
    max_tokens: Optional[int] = Form(1024),
    temperature: Optional[float] = Form(0.7)
):
    """文本生成"""
    try:
        response = await xinference_service.generate_text(
            prompt=prompt,
            model_name=model_name,
            max_tokens=max_tokens,
            temperature=temperature
        )
        
        return {
            "status": "success",
            "data": response
        }
    except Exception as e:
        logger.error(f"Text generation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/llm/analyze-frame")
async def analyze_frame(
    frame_path: str = Form(...),
    question: str = Form("请详细描述这张图片的内容"),
    model_name: Optional[str] = Form(None)
):
    """分析视频帧"""
    try:
        response = await xinference_service.analyze_video_frame(
            frame_path=frame_path,
            question=question,
            model_name=model_name
        )
        
        return {
            "status": "success",
            "data": response
        }
    except Exception as e:
        logger.error(f"Frame analysis failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Audio 端点
@router.post("/audio/transcribe")
async def transcribe_audio(
    audio_file: UploadFile = File(...),
    model_name: Optional[str] = Form(None),
    language: Optional[str] = Form(None)
):
    """语音转文字"""
    try:
        # 保存上传的文件到临时位置
        import tempfile
        import os
        
        with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(audio_file.filename)[1]) as temp_file:
            content = await audio_file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name
        
        try:
            response = await xinference_service.transcribe_audio(
                audio_file_path=temp_file_path,
                model_name=model_name,
                language=language
            )
            
            return {
                "status": "success",
                "data": response
            }
        finally:
            # 清理临时文件
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
                
    except Exception as e:
        logger.error(f"Audio transcription failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/audio/transcribe-video")
async def transcribe_video(
    video_path: str = Form(...),
    model_name: Optional[str] = Form(None),
    language: Optional[str] = Form(None)
):
    """转录视频中的语音"""
    try:
        response = await xinference_service.transcribe_video(
            video_path=video_path,
            model_name=model_name,
            language=language
        )

        return {
            "status": "success",
            "data": response
        }
    except Exception as e:
        logger.error(f"Video transcription failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/audio/generate-subtitle")
async def generate_subtitle_with_xinference(
    video_id: int = Form(...),
    audio_track_id: Optional[int] = Form(None),
    model_name: Optional[str] = Form(None),
    language: Optional[str] = Form("zh"),
    db: Session = Depends(get_db)
):
    """使用 Xinference 生成视频字幕"""
    try:
        from app.services.xinference_subtitle_service import get_xinference_subtitle_service

        xinference_subtitle_service = get_xinference_subtitle_service(db)

        # 检查服务是否可用
        if not xinference_subtitle_service.is_available():
            raise HTTPException(status_code=503, detail="Xinference service is not available")

        # 生成字幕
        # 注意：当前实现中model_name和language参数在xinference_subtitle_service中处理
        # 这里传递给服务，但服务内部会使用默认配置
        subtitle = await xinference_subtitle_service.generate_automatic_subtitle(
            video_id=video_id,
            audio_track_id=audio_track_id
        )

        logger.info(f"Generated subtitle with Xinference - Model: {model_name or 'default'}, Language: {language}")

        return {
            "status": "success",
            "message": "Subtitle generated successfully with Xinference",
            "data": {
                "video_id": video_id,
                "subtitle_id": subtitle.id,
                "subtitle_type": subtitle.subtitle_type,
                "language": subtitle.language,
                "confidence": subtitle.confidence,
                "processing_time": subtitle.processing_time,
                "file_path": subtitle.file_path
            }
        }
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except FileNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Xinference subtitle generation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Embedding 端点
@router.post("/embedding/create")
async def create_embedding(
    text: str = Form(...),
    model_name: Optional[str] = Form(None)
):
    """创建文本向量"""
    try:
        response = await xinference_service.create_embedding(
            text=text,
            model_name=model_name
        )
        
        return {
            "status": "success",
            "data": response
        }
    except Exception as e:
        logger.error(f"Embedding creation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/embedding/similarity")
async def compute_similarity(
    text1: str = Form(...),
    text2: str = Form(...),
    model_name: Optional[str] = Form(None)
):
    """计算文本相似度"""
    try:
        response = await xinference_service.compute_similarity(
            text1=text1,
            text2=text2,
            model_name=model_name
        )
        
        return {
            "status": "success",
            "data": response
        }
    except Exception as e:
        logger.error(f"Similarity computation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/embedding/search")
async def semantic_search(
    query: str = Form(...),
    documents: str = Form(...),  # JSON 格式的文档列表
    text_field: str = Form("content"),
    top_k: int = Form(10),
    model_name: Optional[str] = Form(None)
):
    """语义搜索"""
    try:
        # 解析文档列表
        if isinstance(documents, str):
            documents = json.loads(documents)
        
        response = await xinference_service.semantic_search(
            query=query,
            documents=documents,
            text_field=text_field,
            top_k=top_k,
            model_name=model_name
        )
        
        return {
            "status": "success",
            "data": response
        }
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Invalid JSON format for documents")
    except Exception as e:
        logger.error(f"Semantic search failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 批量处理端点
@router.post("/batch/transcribe")
async def batch_transcribe(
    file_paths: List[str] = Form(...),
    model_name: Optional[str] = Form(None),
    language: Optional[str] = Form(None)
):
    """批量转录音频"""
    try:
        # 解析文件路径列表
        if isinstance(file_paths, str):
            file_paths = json.loads(file_paths)
        
        response = await xinference_service.batch_transcribe(
            file_paths=file_paths,
            model_name=model_name,
            language=language
        )
        
        return {
            "status": "success",
            "data": response
        }
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Invalid JSON format for file_paths")
    except Exception as e:
        logger.error(f"Batch transcription failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 维护端点
@router.post("/maintenance/cleanup-idle")
async def cleanup_idle_models(idle_timeout: int = Form(3600)):
    """清理空闲模型"""
    try:
        await xinference_service.cleanup_idle_models(idle_timeout)
        return {
            "status": "success",
            "message": f"Idle models cleaned up (timeout: {idle_timeout}s)"
        }
    except Exception as e:
        logger.error(f"Cleanup failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/maintenance/auto-launch")
async def auto_launch_models():
    """自动启动模型"""
    try:
        await xinference_service.auto_launch_models()
        return {
            "status": "success",
            "message": "Auto-launch completed"
        }
    except Exception as e:
        logger.error(f"Auto-launch failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))
