"""
Xinference 模型配置管理
"""

from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field
from enum import Enum


class ModelType(str, Enum):
    """模型类型枚举"""
    LLM = "llm"
    AUDIO = "audio"
    EMBEDDING = "embedding"
    IMAGE = "image"
    RERANK = "rerank"


class ModelEngine(str, Enum):
    """模型引擎枚举"""
    TRANSFORMERS = "transformers"
    LLAMA_CPP = "llama.cpp"
    VLLM = "vllm"
    TENSORRT_LLM = "tensorrt_llm"


class ModelFormat(str, Enum):
    """模型格式枚举"""
    PYTORCH = "pytorch"
    GGUFV2 = "ggufv2"
    GGMLV3 = "ggmlv3"
    AWQV4 = "awqv4"


class ModelConfig(BaseModel):
    """单个模型配置"""
    name: str = Field(..., description="模型名称")
    model_type: ModelType = Field(..., description="模型类型")
    engine: Optional[ModelEngine] = Field(None, description="推理引擎")
    format: Optional[ModelFormat] = Field(None, description="模型格式")
    size_in_billions: Optional[int] = Field(None, description="模型大小（十亿参数）")
    quantization: Optional[str] = Field(None, description="量化方式")
    dimensions: Optional[int] = Field(None, description="向量维度（embedding模型）")
    multilingual: Optional[bool] = Field(None, description="是否支持多语言（audio模型）")
    family: Optional[str] = Field(None, description="模型家族（image模型）")
    description: Optional[str] = Field(None, description="模型描述")
    enabled: bool = Field(True, description="是否启用")
    auto_launch: bool = Field(False, description="是否自动启动")
    launch_params: Optional[Dict[str, Any]] = Field(default_factory=dict, description="启动参数")


class XinferenceModelRegistry(BaseModel):
    """Xinference 模型注册表"""
    llm_models: List[ModelConfig] = Field(default_factory=list, description="LLM 模型列表")
    audio_models: List[ModelConfig] = Field(default_factory=list, description="Audio 模型列表")
    embedding_models: List[ModelConfig] = Field(default_factory=list, description="Embedding 模型列表")
    image_models: List[ModelConfig] = Field(default_factory=list, description="Image 模型列表")
    rerank_models: List[ModelConfig] = Field(default_factory=list, description="Rerank 模型列表")
    
    def get_models_by_type(self, model_type: ModelType) -> List[ModelConfig]:
        """根据类型获取模型列表"""
        type_mapping = {
            ModelType.LLM: self.llm_models,
            ModelType.AUDIO: self.audio_models,
            ModelType.EMBEDDING: self.embedding_models,
            ModelType.IMAGE: self.image_models,
            ModelType.RERANK: self.rerank_models,
        }
        return type_mapping.get(model_type, [])
    
    def get_enabled_models(self, model_type: Optional[ModelType] = None) -> List[ModelConfig]:
        """获取启用的模型列表"""
        if model_type:
            return [model for model in self.get_models_by_type(model_type) if model.enabled]
        
        all_models = []
        for models in [self.llm_models, self.audio_models, self.embedding_models, 
                      self.image_models, self.rerank_models]:
            all_models.extend([model for model in models if model.enabled])
        return all_models
    
    def get_model_by_name(self, name: str) -> Optional[ModelConfig]:
        """根据名称获取模型配置"""
        for models in [self.llm_models, self.audio_models, self.embedding_models,
                      self.image_models, self.rerank_models]:
            for model in models:
                if model.name == name:
                    return model
        return None
    
    def add_model(self, model: ModelConfig):
        """添加模型配置"""
        type_mapping = {
            ModelType.LLM: self.llm_models,
            ModelType.AUDIO: self.audio_models,
            ModelType.EMBEDDING: self.embedding_models,
            ModelType.IMAGE: self.image_models,
            ModelType.RERANK: self.rerank_models,
        }
        model_list = type_mapping.get(model.model_type)
        if model_list is not None:
            # 检查是否已存在同名模型
            existing = next((m for m in model_list if m.name == model.name), None)
            if existing:
                # 更新现有模型
                model_list[model_list.index(existing)] = model
            else:
                # 添加新模型
                model_list.append(model)


# 默认模型配置
DEFAULT_MODEL_REGISTRY = XinferenceModelRegistry(
    llm_models=[
        # ModelConfig(
        #     name="glm4-chat",
        #     model_type=ModelType.LLM,
        #     engine=ModelEngine.LLAMA_CPP,
        #     format=ModelFormat.GGUFV2,
        #     size_in_billions=9,
        #     quantization="Q4_K",
        #     description="GLM4 对话模型",
        #     auto_launch=True
        # ),
        # ModelConfig(
        #     name="qwen2.5-chat",
        #     model_type=ModelType.LLM,
        #     engine=ModelEngine.TRANSFORMERS,
        #     size_in_billions=7,
        #     description="Qwen2.5 对话模型"
        # ),
    ],
    audio_models=[
        ModelConfig(
            name="paraformer-zh-spk",
            model_type=ModelType.AUDIO,
            multilingual=False,
            description="Paraformer 中文语音识别模型",
            auto_launch=True
        ),
        # ModelConfig(
        #     name="whisper-large-v3",
        #     model_type=ModelType.AUDIO,
        #     multilingual=True,
        #     description="Whisper Large V3 语音识别模型"
        # ),
        # ModelConfig(
        #     name="whisper-base",
        #     model_type=ModelType.AUDIO,
        #     multilingual=True,
        #     description="Whisper Base 语音识别模型"
        # ),
    ],
    embedding_models=[
        # ModelConfig(
        #     name="bge-large-zh-v1.5",
        #     model_type=ModelType.EMBEDDING,
        #     dimensions=1024,
        #     description="BGE Large 中文向量模型",
        #     auto_launch=True
        # ),
        # ModelConfig(
        #     name="bge-small-en-v1.5",
        #     model_type=ModelType.EMBEDDING,
        #     dimensions=384,
        #     description="BGE Small 英文向量模型"
        # ),
    ],
    image_models=[
        # ModelConfig(
        #     name="stable-diffusion-xl-base-1.0",
        #     model_type=ModelType.IMAGE,
        #     family="stable_diffusion",
        #     description="Stable Diffusion XL 文生图模型"
        # ),
        # ModelConfig(
        #     name="stable-diffusion-v1.5",
        #     model_type=ModelType.IMAGE,
        #     family="stable_diffusion",
        #     description="Stable Diffusion V1.5 文生图模型"
        # ),
    ],
    rerank_models=[
        # ModelConfig(
        #     name="bge-reranker-large",
        #     model_type=ModelType.RERANK,
        #     description="BGE Large 重排序模型"
        # ),
        # ModelConfig(
        #     name="bge-reranker-base",
        #     model_type=ModelType.RERANK,
        #     description="BGE Base 重排序模型"
        # ),
    ]
)


def get_default_model_name(model_type: ModelType) -> str:
    """获取默认模型名称"""
    from app.core.config import settings
    
    type_mapping = {
        ModelType.LLM: settings.XINFERENCE_DEFAULT_LLM,
        ModelType.AUDIO: settings.XINFERENCE_DEFAULT_AUDIO,
        ModelType.EMBEDDING: settings.XINFERENCE_DEFAULT_EMBEDDING,
        ModelType.IMAGE: settings.XINFERENCE_DEFAULT_IMAGE,
        ModelType.RERANK: settings.XINFERENCE_DEFAULT_RERANK,
    }
    return type_mapping.get(model_type, "")


def load_model_registry() -> XinferenceModelRegistry:
    """加载模型注册表"""
    # TODO: 从数据库或配置文件加载自定义模型配置
    # 目前返回默认配置
    return DEFAULT_MODEL_REGISTRY


# 全局模型注册表实例
model_registry = load_model_registry()
