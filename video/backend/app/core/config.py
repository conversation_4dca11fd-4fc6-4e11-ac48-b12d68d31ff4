"""
应用配置管理
"""

from pydantic_settings import BaseSettings
from typing import List
import os


class Settings(BaseSettings):
    """应用配置类"""
    
    # 基础配置
    APP_NAME: str = "短剧视频分析剪辑软件"
    DEBUG: bool = False
    VERSION: str = "1.0.0"
    
    # 服务器配置
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    ALLOWED_HOSTS: List[str] = ["*"]
    
    # 数据库配置
    DATABASE_URL: str = "sqlite:///./app.db"
    
    # Redis配置
    REDIS_URL: str = "redis://localhost:6379/0"
    
    # Celery配置
    CELERY_BROKER_URL: str = "redis://localhost:6379/1"
    CELERY_RESULT_BACKEND: str = "redis://localhost:6379/2"
    
    # JWT配置
    SECRET_KEY: str = "your-secret-key-here"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # 文件存储配置
    UPLOAD_DIR: str = "../storage/uploads"
    VIDEOS_STORAGE_DIR: str = "../storage/videos"
    MAX_FILE_SIZE: int = 1024 * 1024 * 1024 * 2
    
    # 视频处理配置
    VIDEO_FORMATS: List[str] = [".mp4", ".mov", ".avi", ".mkv", ".wmv"]
    
    # 任务配置
    MAX_CONCURRENT_TASKS: int = 3
    TASK_TIMEOUT: int = 3600
    
    # MiniCPM-V-4 配置
    MINICPM_MODEL_NAME: str = "openbmb/MiniCPM-V-4"
    MINICPM_DEVICE: str = "cuda"  # cuda or cpu
    MINICPM_TORCH_DTYPE: str = "bfloat16"  # bfloat16, float16, float32
    MINICPM_ATTENTION_IMPLEMENTATION: str = "sdpa"  # sdpa or flash_attention_2
    MINICPM_MAX_FRAMES_PER_ANALYSIS: int = 10  # 每次分析的最大帧数
    MINICPM_FRAME_SAMPLING_INTERVAL: int = 5  # 帧采样间隔

    # 音频处理配置
    ENABLE_PARSE_AUDIO: bool = False
    AUDIO_BATCH_SIZE: int = 10
    AUDIO_BATCH_SIZE_S: int = 300
    AUDIO_BATCH_SIZE_THRESHOLD_S: int = 60
    AUDIO_MAX_FILE_SIZE: int = 100  # MB
    AUDIO_TEMP_DIR: str = "../storage/tmp/audio_processing"
    AUDIO_CACHE_ENABLED: bool = True
    AUDIO_MAX_CONCURRENT_TASKS: int = 3

    # Xinference 配置
    XINFERENCE_ENABLED: bool = True  # 是否启用 Xinference
    XINFERENCE_BASE_URL: str = "http://100.124.255.81:9997"  # Xinference 服务地址
    XINFERENCE_API_KEY: str = ""  # API 密钥（可选）
    XINFERENCE_TIMEOUT: int = 30  # 请求超时时间（秒）
    XINFERENCE_MAX_RETRIES: int = 3  # 最大重试次数
    XINFERENCE_CONNECTION_POOL_SIZE: int = 10  # 连接池大小

    # Xinference 模型配置
    XINFERENCE_DEFAULT_LLM: str = "glm4-chat"
    XINFERENCE_DEFAULT_AUDIO: str = "paraformer-zh-spk"
    XINFERENCE_DEFAULT_EMBEDDING: str = "bge-large-zh-v1.5"
    XINFERENCE_DEFAULT_IMAGE: str = "stable-diffusion-xl-base-1.0"
    XINFERENCE_DEFAULT_RERANK: str = "bge-reranker-large"

    # Xinference 性能配置
    XINFERENCE_MAX_CONCURRENT_REQUESTS: int = 5  # 最大并发请求数
    XINFERENCE_CACHE_ENABLED: bool = True  # 是否启用结果缓存
    XINFERENCE_CACHE_TTL: int = 3600  # 缓存过期时间（秒）

    # 场景检测配置
    SCENE_DETECTION_THRESHOLD: float = 30.0  # 场景检测阈值
    SCENE_MIN_LENGTH: float = 1.0  # 最小场景长度（秒）
    SCENE_DETECTION_ENABLED: bool = True  # 是否启用场景检测
    SCENE_AUTO_SPLIT_ENABLED: bool = True  # 是否在基础分析中自动分割场景
    SCENE_SPLIT_FORMAT: str = "mp4"  # 场景分割输出格式
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# 创建配置实例
settings = Settings()

# 确保存储目录存在
for directory in [
    settings.UPLOAD_DIR,
    settings.VIDEOS_STORAGE_DIR
]:
    os.makedirs(directory, exist_ok=True)
