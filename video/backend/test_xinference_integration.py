#!/usr/bin/env python3
"""
Xinference 集成测试脚本
测试 Xinference 服务的各项功能
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "app"))

from app.core.config import settings
from app.services.xinference_service import xinference_service
from app.core.xinference_config import ModelType, model_registry


async def test_health_check():
    """测试健康检查"""
    print("🔍 Testing health check...")
    try:
        health = await xinference_service.health_check()
        print(f"✅ Health check: {health['status']}")
        return health['status'] in ['healthy', 'disabled']
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False


async def test_model_registry():
    """测试模型注册表"""
    print("🔍 Testing model registry...")
    try:
        available_models = await xinference_service.list_available_models()
        print(f"✅ Available models: {len(available_models)} types")
        
        for model_type, models in available_models.items():
            print(f"  - {model_type}: {len(models)} models")
            for model in models[:2]:  # 只显示前2个
                print(f"    * {model['name']}: {model['description']}")
        
        return True
    except Exception as e:
        print(f"❌ Model registry test failed: {e}")
        return False


async def test_model_management():
    """测试模型管理"""
    if not xinference_service.enabled:
        print("⏭️ Skipping model management test (Xinference disabled)")
        return True
    
    print("🔍 Testing model management...")
    try:
        # 获取运行中的模型
        running_models = await xinference_service.get_running_models()
        print(f"✅ Running models: {len(running_models)}")
        
        # 尝试启动一个小模型（如果没有运行的话）
        if not running_models:
            print("🚀 Attempting to launch a test model...")
            # 选择一个较小的模型进行测试
            test_model = "whisper-base"  # 相对较小的模型
            
            model_uid = await xinference_service.launch_model_by_name(test_model)
            if model_uid:
                print(f"✅ Model launched: {model_uid}")
                
                # 获取模型信息
                model_info = await xinference_service.get_model_info(model_uid)
                print(f"✅ Model info retrieved: {model_info is not None}")
                
                # 停止模型
                success = await xinference_service.terminate_model_by_uid(model_uid)
                print(f"✅ Model terminated: {success}")
            else:
                print("⚠️ Failed to launch test model")
        
        return True
    except Exception as e:
        print(f"❌ Model management test failed: {e}")
        return False


async def test_llm_service():
    """测试 LLM 服务"""
    if not xinference_service.enabled:
        print("⏭️ Skipping LLM test (Xinference disabled)")
        return True
    
    print("🔍 Testing LLM service...")
    try:
        # 测试简单的文本生成
        response = await xinference_service.generate_text(
            prompt="Hello, how are you?",
            max_tokens=50
        )
        
        if response and "choices" in response:
            generated_text = response["choices"][0].get("text", "")
            print(f"✅ LLM generation: {len(generated_text)} characters")
            print(f"   Sample: {generated_text[:100]}...")
            return True
        else:
            print("⚠️ LLM response format unexpected")
            return False
            
    except Exception as e:
        print(f"❌ LLM test failed: {e}")
        return False


async def test_audio_service():
    """测试音频服务"""
    if not xinference_service.enabled:
        print("⏭️ Skipping audio test (Xinference disabled)")
        return True
    
    print("🔍 Testing audio service...")
    try:
        # 创建一个测试音频文件路径（实际测试中需要真实文件）
        test_audio_path = "/tmp/test_audio.wav"
        
        if os.path.exists(test_audio_path):
            result = await xinference_service.transcribe_audio(test_audio_path)
            print(f"✅ Audio transcription: {len(result.get('text', ''))} characters")
            return True
        else:
            print("⏭️ Skipping audio test (no test file)")
            return True
            
    except Exception as e:
        print(f"❌ Audio test failed: {e}")
        return False


async def test_embedding_service():
    """测试 Embedding 服务"""
    if not xinference_service.enabled:
        print("⏭️ Skipping embedding test (Xinference disabled)")
        return True
    
    print("🔍 Testing embedding service...")
    try:
        # 测试文本向量化
        result = await xinference_service.create_embedding("Hello world")
        
        if result and "data" in result:
            embedding = result["data"][0]["embedding"]
            print(f"✅ Embedding creation: {len(embedding)} dimensions")
            
            # 测试相似度计算
            similarity = await xinference_service.compute_similarity(
                "Hello world", "Hi there"
            )
            print(f"✅ Similarity computation: {similarity['cosine_similarity']:.3f}")
            return True
        else:
            print("⚠️ Embedding response format unexpected")
            return False
            
    except Exception as e:
        print(f"❌ Embedding test failed: {e}")
        return False


async def test_configuration():
    """测试配置"""
    print("🔍 Testing configuration...")
    try:
        print(f"✅ Xinference enabled: {settings.XINFERENCE_ENABLED}")
        print(f"✅ Base URL: {settings.XINFERENCE_BASE_URL}")
        print(f"✅ Timeout: {settings.XINFERENCE_TIMEOUT}s")
        print(f"✅ Max retries: {settings.XINFERENCE_MAX_RETRIES}")
        
        # 测试模型配置
        default_models = {
            "LLM": settings.XINFERENCE_DEFAULT_LLM,
            "Audio": settings.XINFERENCE_DEFAULT_AUDIO,
            "Embedding": settings.XINFERENCE_DEFAULT_EMBEDDING,
            "Image": settings.XINFERENCE_DEFAULT_IMAGE,
            "Rerank": settings.XINFERENCE_DEFAULT_RERANK,
        }
        
        print("✅ Default models:")
        for model_type, model_name in default_models.items():
            print(f"   - {model_type}: {model_name}")
        
        return True
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False


async def run_all_tests():
    """运行所有测试"""
    print("🧪 Starting Xinference integration tests...\n")
    
    tests = [
        ("Configuration", test_configuration),
        ("Health Check", test_health_check),
        ("Model Registry", test_model_registry),
        ("Model Management", test_model_management),
        ("LLM Service", test_llm_service),
        ("Audio Service", test_audio_service),
        ("Embedding Service", test_embedding_service),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Running: {test_name}")
        print('='*50)
        
        try:
            result = await test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ Test {test_name} crashed: {e}")
            results[test_name] = False
    
    # 总结结果
    print(f"\n{'='*50}")
    print("TEST SUMMARY")
    print('='*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
        return 0
    else:
        print("⚠️ Some tests failed. Check the output above.")
        return 1


async def main():
    """主函数"""
    try:
        # 初始化服务
        await xinference_service.initialize()
        
        # 运行测试
        exit_code = await run_all_tests()
        
        # 清理
        await xinference_service.cleanup()
        
        return exit_code
        
    except KeyboardInterrupt:
        print("\n🛑 Tests interrupted by user")
        return 1
    except Exception as e:
        print(f"\n💥 Test suite crashed: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
