#!/usr/bin/env python3
"""
测试 Xinference Audio 服务集成
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.core.config import settings
from app.services.xinference_audio_service import XinferenceAudioService
from app.services.xinference_client_service import xinference_client
from loguru import logger


async def test_xinference_connection():
    """测试 Xinference 连接"""
    print("=" * 50)
    print("测试 Xinference 连接")
    print("=" * 50)
    
    try:
        # 测试健康检查
        health = await xinference_client.health_check()
        print(f"✅ Xinference 健康检查: {health}")
        
        # 列出可用模型
        available_models = await xinference_client.list_available_models()
        print(f"✅ 可用模型数量: {len(available_models)}")
        
        # 列出运行中的模型
        running_models = await xinference_client.get_running_models()
        print(f"✅ 运行中的模型数量: {len(running_models)}")
        
        # 查找音频模型
        audio_models = [model for model in available_models if model.get('model_type') == 'audio']
        print(f"✅ 可用音频模型数量: {len(audio_models)}")
        
        for model in audio_models[:3]:  # 显示前3个
            print(f"   - {model.get('model_name', 'Unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Xinference 连接失败: {e}")
        return False


async def test_audio_model_launch():
    """测试音频模型启动"""
    print("\n" + "=" * 50)
    print("测试音频模型启动")
    print("=" * 50)
    
    try:
        # 尝试启动默认音频模型
        default_model = settings.XINFERENCE_DEFAULT_AUDIO
        print(f"尝试启动默认音频模型: {default_model}")
        
        from app.core.xinference_config import ModelType
        model_uid = await xinference_client.ensure_model_available(default_model, ModelType.AUDIO)
        
        if model_uid:
            print(f"✅ 音频模型启动成功: {model_uid}")
            
            # 获取模型信息
            model_info = await xinference_client.get_model_info(model_uid)
            print(f"✅ 模型信息: {model_info}")
            
            return model_uid
        else:
            print(f"❌ 音频模型启动失败")
            return None
            
    except Exception as e:
        print(f"❌ 音频模型启动异常: {e}")
        return None


async def test_audio_transcription():
    """测试音频转录功能"""
    print("\n" + "=" * 50)
    print("测试音频转录功能")
    print("=" * 50)
    
    try:
        # 创建音频服务实例
        audio_service = XinferenceAudioService()
        
        # 查找测试音频文件
        test_audio_paths = [
            "../storage/videos/*/audios/*.wav",
            "../storage/videos/*/audios/*.mp3",
            "../storage/tmp/audio_processing/*.wav"
        ]
        
        import glob
        test_file = None
        for pattern in test_audio_paths:
            files = glob.glob(pattern)
            if files:
                test_file = files[0]
                break
        
        if not test_file:
            print("❌ 未找到测试音频文件")
            print("提示: 请先上传视频并提取音频，或者手动放置测试音频文件")
            return False
        
        print(f"使用测试文件: {test_file}")
        
        # 检查文件是否存在
        if not os.path.exists(test_file):
            print(f"❌ 测试文件不存在: {test_file}")
            return False
        
        # 获取文件大小
        file_size = os.path.getsize(test_file) / 1024 / 1024  # MB
        print(f"文件大小: {file_size:.2f} MB")
        
        # 执行转录
        print("开始转录...")
        result = await audio_service.transcribe_file(test_file, language="zh")
        
        print("✅ 转录成功!")
        print(f"转录结果: {result.get('text', 'No text')[:200]}...")
        
        if 'segments' in result:
            print(f"段落数量: {len(result['segments'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ 音频转录失败: {e}")
        return False


async def test_subtitle_generation():
    """测试字幕生成功能"""
    print("\n" + "=" * 50)
    print("测试字幕生成功能")
    print("=" * 50)
    
    try:
        # 这里需要数据库连接，暂时跳过
        print("⚠️  字幕生成测试需要数据库连接，暂时跳过")
        print("可以通过 API 端点测试: POST /api/v1/xinference/audio/generate-subtitle")
        return True
        
    except Exception as e:
        print(f"❌ 字幕生成测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("Xinference Audio 服务集成测试")
    print(f"Xinference 服务地址: {settings.XINFERENCE_BASE_URL}")
    print(f"默认音频模型: {settings.XINFERENCE_DEFAULT_AUDIO}")
    print(f"Xinference 启用状态: {settings.XINFERENCE_ENABLED}")
    
    if not settings.XINFERENCE_ENABLED:
        print("❌ Xinference 服务未启用，请检查配置")
        return
    
    # 运行测试
    tests = [
        ("连接测试", test_xinference_connection),
        ("模型启动测试", test_audio_model_launch),
        ("音频转录测试", test_audio_transcription),
        ("字幕生成测试", test_subtitle_generation),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果摘要
    print("\n" + "=" * 50)
    print("测试结果摘要")
    print("=" * 50)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！Xinference Audio 服务集成成功")
    else:
        print("⚠️  部分测试失败，请检查配置和服务状态")


if __name__ == "__main__":
    asyncio.run(main())
