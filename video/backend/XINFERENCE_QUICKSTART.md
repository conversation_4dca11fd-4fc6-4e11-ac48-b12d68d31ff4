# Xinference 快速开始指南

## 概述

本指南将帮助您快速在视频分析系统中集成和使用 Xinference 服务。

## 前置条件

1. **安装 Xinference**
   ```bash
   pip install xinference
   ```

2. **启动 Xinference 服务**
   ```bash
   xinference
   ```
   
   服务将在 `http://localhost:9997` 启动。

## 配置

### 1. 环境变量配置

在 `video/backend/.env` 文件中添加：

```bash
# 启用 Xinference
XINFERENCE_ENABLED=true
XINFERENCE_BASE_URL=http://localhost:9997
XINFERENCE_TIMEOUT=30
XINFERENCE_MAX_RETRIES=3

# 默认模型配置
XINFERENCE_DEFAULT_LLM=glm4-chat
XINFERENCE_DEFAULT_AUDIO=whisper-large-v3
XINFERENCE_DEFAULT_EMBEDDING=bge-large-zh-v1.5
```

### 2. 启动应用

```bash
cd video/backend
python -m uvicorn app.main:app --reload
```

## 基础使用

### 1. 健康检查

```bash
curl http://localhost:8000/api/v1/xinference/health
```

预期响应：
```json
{
  "status": "success",
  "data": {
    "status": "healthy",
    "initialized": true,
    "base_url": "http://localhost:9997",
    "running_models": 0
  }
}
```

### 2. 查看可用模型

```bash
curl http://localhost:8000/api/v1/xinference/models/available
```

### 3. 启动模型

```bash
# 启动 LLM 模型
curl -X POST http://localhost:8000/api/v1/xinference/models/glm4-chat/launch

# 启动音频模型
curl -X POST http://localhost:8000/api/v1/xinference/models/whisper-large-v3/launch
```

## 功能演示

### 1. 文本生成

```bash
curl -X POST http://localhost:8000/api/v1/xinference/llm/generate \
  -F "prompt=请介绍一下人工智能的发展历史" \
  -F "max_tokens=200" \
  -F "temperature=0.7"
```

### 2. 对话功能

```bash
curl -X POST http://localhost:8000/api/v1/xinference/llm/chat \
  -F 'messages=[{"role":"user","content":"你好，请介绍一下自己"}]' \
  -F "max_tokens=100"
```

### 3. 音频转录

准备一个音频文件 `test.wav`，然后：

```bash
curl -X POST http://localhost:8000/api/v1/xinference/audio/transcribe \
  -F "audio_file=@test.wav" \
  -F "language=zh"
```

### 4. 文本向量化

```bash
curl -X POST http://localhost:8000/api/v1/xinference/embedding/create \
  -F "text=这是一段测试文本，用于生成向量表示"
```

### 5. 语义相似度

```bash
curl -X POST http://localhost:8000/api/v1/xinference/embedding/similarity \
  -F "text1=苹果是一种水果" \
  -F "text2=橙子也是水果"
```

## 视频分析集成

### 1. 综合视频分析

假设您有一个 ID 为 1 的视频：

```bash
curl -X POST http://localhost:8000/api/v1/videos/1/xinference/comprehensive-analysis \
  -F "use_xinference=true" \
  -F "include_audio=true" \
  -F "include_visual=true" \
  -F "include_scenes=true" \
  -F "max_frames=5"
```

这将执行：
- 基础视频元数据分析
- 音频转录（使用 Xinference）
- 视觉内容分析（使用 LLM 分析关键帧）
- 场景分析
- 生成综合报告

### 2. 视频音频转录

```bash
curl -X POST http://localhost:8000/api/v1/videos/1/xinference/transcribe \
  -F "use_xinference=true" \
  -F "language=zh"
```

### 3. 转录服务比较

比较 Xinference 和本地服务的转录效果：

```bash
curl -X POST http://localhost:8000/api/v1/videos/1/xinference/compare-transcription \
  -F "language=zh"
```

## Python 代码示例

### 1. 基础使用

```python
import asyncio
from app.services.xinference_service import xinference_service

async def main():
    # 初始化服务
    await xinference_service.initialize()
    
    # 文本生成
    response = await xinference_service.generate_text(
        prompt="请写一首关于春天的诗",
        max_tokens=100
    )
    print("生成的文本:", response)
    
    # 音频转录
    transcription = await xinference_service.transcribe_audio(
        "audio.wav",
        language="zh"
    )
    print("转录结果:", transcription["text"])
    
    # 文本向量化
    embedding = await xinference_service.create_embedding(
        "这是一段测试文本"
    )
    print("向量维度:", len(embedding["data"][0]["embedding"]))
    
    # 清理
    await xinference_service.cleanup()

# 运行
asyncio.run(main())
```

### 2. 视频分析

```python
from app.services.enhanced_video_analysis_service import create_enhanced_video_analysis_service
from app.core.database import get_db

async def analyze_video():
    db = next(get_db())
    service = create_enhanced_video_analysis_service(db)
    
    result = await service.comprehensive_video_analysis(
        video_id=1,
        use_xinference=True,
        analysis_options={
            "include_audio": True,
            "include_visual": True,
            "include_scenes": True,
            "visual_options": {"max_frames": 5}
        }
    )
    
    print("分析结果:", result)
```

## 测试

### 运行集成测试

```bash
cd video/backend
python test_xinference_integration.py
```

### 测试特定功能

```python
# 测试健康检查
curl http://localhost:8000/api/v1/xinference/health

# 测试模型列表
curl http://localhost:8000/api/v1/xinference/models/available

# 测试简单生成
curl -X POST http://localhost:8000/api/v1/xinference/llm/generate \
  -F "prompt=Hello" \
  -F "max_tokens=10"
```

## 常见问题

### 1. 连接失败

**问题**: `Failed to connect to Xinference service`

**解决方案**:
- 确认 Xinference 服务正在运行：`ps aux | grep xinference`
- 检查端口是否正确：`netstat -an | grep 9997`
- 验证配置：检查 `XINFERENCE_BASE_URL`

### 2. 模型启动失败

**问题**: `Failed to launch model`

**解决方案**:
- 检查系统资源（内存、GPU）
- 确认模型名称正确
- 查看 Xinference 日志：`xinference logs`

### 3. 转录失败

**问题**: `Audio transcription failed`

**解决方案**:
- 确认音频文件格式支持
- 检查文件大小限制
- 验证音频模型是否已启动

## 下一步

1. **性能优化**: 根据使用情况调整模型配置和并发设置
2. **模型管理**: 设置自动启动和清理策略
3. **监控**: 配置日志和性能监控
4. **扩展**: 添加更多模型类型和自定义分析功能

## 获取帮助

- 查看完整文档：`XINFERENCE_INTEGRATION.md`
- 运行测试：`python test_xinference_integration.py`
- 查看 API 文档：访问 `http://localhost:8000/docs`
- Xinference 官方文档：https://inference.readthedocs.io/
