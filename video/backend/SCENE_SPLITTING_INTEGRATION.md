# 场景分割集成和修复文档

## 概述

本文档描述了场景分割功能的修复和集成到视频基础分析任务中的改进。

## 问题修复

### 1. split_video_ffmpeg 参数错误修复

**问题**: `split_video_ffmpeg() got an unexpected keyword argument 'input_video_paths'`

**原因**: PySceneDetect 更新后，函数参数从 `input_video_paths` 改为 `input_video_path`

**修复**:
```python
# 修复前
output_files = split_video_ffmpeg(
    input_video_paths=[video.file_path],  # 错误：复数形式
    ...
)

# 修复后
return_code = split_video_ffmpeg(
    input_video_path=video.file_path,  # 正确：单数形式
    ...
)
```

### 2. 返回值类型错误修复

**问题**: `object of type 'int' has no len()`

**原因**: `split_video_ffmpeg` 函数返回的是整数状态码，不是文件列表

**修复**:
```python
# 修复前
output_files = split_video_ffmpeg(...)  # 期望返回文件列表
scene_clips = self._save_scene_clips_to_db(video_id, scenes, output_files, output_format)

# 修复后
return_code = split_video_ffmpeg(...)  # 返回状态码
if return_code != 0:
    raise RuntimeError(f"FFmpeg failed with return code {return_code}")
scene_clips = self._save_scene_clips_to_db(video_id, scenes, output_format)
```

### 3. HEAD 方法支持

**问题**: `/api/v1/videos/{video_id}/scenes/{scene_number}/file` 端点不支持 HEAD 请求

**修复**:
```python
@router.get("/{video_id}/scenes/{scene_number}/file")
@router.head("/{video_id}/scenes/{scene_number}/file")  # 添加HEAD方法支持
async def get_scene_file(...):
    ...
```

## 功能集成

### 1. 场景分割集成到基础分析任务

**改进**: 将场景分割从手动触发改为自动执行

**实现位置**: `video/backend/app/tasks/video_tasks_basic_info.py`

**主要变化**:
```python
# 步骤7: 镜头检测和分割
task_logger.start_step("检测镜头切换并分割视频")

# 首先检测场景
scenes = scene_service.detect_scenes(
    video_id=video_id,
    detector_type="content",
    threshold=settings.SCENE_DETECTION_THRESHOLD,
    min_scene_len=settings.SCENE_MIN_LENGTH
)

# 然后自动分割视频（如果启用）
scene_clips = []
if scenes and settings.SCENE_AUTO_SPLIT_ENABLED:
    try:
        scene_clips = scene_service.split_video_by_scenes(video_id, settings.SCENE_SPLIT_FORMAT)
        task_logger.log_info(f"成功分割视频为 {len(scene_clips)} 个场景片段")
    except Exception as e:
        task_logger.log_warning(f"场景分割失败: {e}")
        # 分割失败不影响整体分析流程
```

### 2. 配置选项

**新增配置** (`app/core/config.py`):
```python
# 场景检测配置
SCENE_DETECTION_THRESHOLD: float = 30.0  # 场景检测阈值
SCENE_MIN_LENGTH: float = 1.0  # 最小场景长度（秒）
SCENE_DETECTION_ENABLED: bool = True  # 是否启用场景检测
SCENE_AUTO_SPLIT_ENABLED: bool = True  # 是否在基础分析中自动分割场景
SCENE_SPLIT_FORMAT: str = "mp4"  # 场景分割输出格式
```

**环境变量配置** (`.env`):
```bash
# 场景检测配置
SCENE_DETECTION_THRESHOLD=30.0
SCENE_MIN_LENGTH=1.0
SCENE_DETECTION_ENABLED=true
SCENE_AUTO_SPLIT_ENABLED=true
SCENE_SPLIT_FORMAT=mp4
```

### 3. 分析结果增强

**结果数据结构更新**:
```python
"scene_detection": {
    "total_scenes": len(scenes),
    "scenes": scenes[:10],
    "avg_scene_duration": avg_scene_duration,
    "scene_clips_generated": len(scene_clips),  # 新增
    "scene_clips": scene_clips[:10] if scene_clips else []  # 新增
}
```

## 工作流程

### 1. 视频上传后的自动处理流程

```mermaid
graph TD
    A[视频上传] --> B[基础信息分析任务]
    B --> C[元数据分析]
    C --> D[音频提取]
    D --> E[关键帧提取]
    E --> F[场景检测]
    F --> G{自动分割启用?}
    G -->|是| H[场景分割]
    G -->|否| I[跳过分割]
    H --> J[保存分割结果]
    I --> K[完成分析]
    J --> K
```

### 2. 场景分割详细流程

```mermaid
graph TD
    A[开始场景分割] --> B[获取场景数据]
    B --> C[构建场景列表]
    C --> D[调用split_video_ffmpeg]
    D --> E{返回码 = 0?}
    E -->|是| F[分割成功]
    E -->|否| G[抛出异常]
    F --> H[构建文件路径]
    H --> I[保存到数据库]
    I --> J[返回场景片段列表]
    G --> K[记录错误日志]
```

## API 变化

### 1. 现有 API 保持兼容

- `/api/v1/videos/{video_id}/scenes/split` - 手动触发场景分割（保留）
- `/api/v1/videos/{video_id}/scenes` - 获取场景列表
- `/api/v1/videos/{video_id}/scenes/{scene_number}/file` - 获取场景文件（现在支持 HEAD 方法）

### 2. 分析结果 API 增强

基础分析结果现在包含场景分割信息：
```json
{
  "scene_detection": {
    "total_scenes": 5,
    "scene_clips_generated": 5,
    "scenes": [...],
    "scene_clips": [...]
  }
}
```

## 优势

### 1. 用户体验改进

- **自动化**: 视频上传后自动完成场景分割，无需手动触发
- **即时可用**: 分析完成后场景片段立即可用
- **错误容忍**: 场景分割失败不影响其他分析步骤

### 2. 系统性能优化

- **批量处理**: 在基础分析中一次性完成所有处理
- **资源利用**: 避免重复加载和处理视频文件
- **存储组织**: 使用标准化的文件组织结构

### 3. 配置灵活性

- **可控制**: 通过配置开关控制是否自动分割
- **可调节**: 检测阈值和参数可配置
- **可扩展**: 支持不同的输出格式

## 测试

### 1. 功能测试

```bash
# 测试场景分割修复
cd video/backend
python test_scene_splitting.py
```

### 2. API 测试

```bash
# 测试 HEAD 方法支持
curl -I http://localhost:8000/api/v1/videos/1/scenes/1/file

# 测试手动场景分割
curl -X POST http://localhost:8000/api/v1/videos/1/scenes/split \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "output_format=mp4"
```

### 3. 集成测试

上传新视频并检查基础分析结果是否包含场景分割信息。

## 故障排除

### 1. 常见问题

**问题**: 场景分割失败但不影响其他分析
**解决**: 检查 FFmpeg 是否正确安装，查看日志了解具体错误

**问题**: 自动分割未执行
**解决**: 检查 `SCENE_AUTO_SPLIT_ENABLED` 配置是否为 true

**问题**: 场景文件未找到
**解决**: 检查文件组织结构和权限设置

### 2. 日志查看

```bash
# 查看场景分割相关日志
grep "scene" logs/app.log
grep "split" logs/app.log
```

## 后续改进

1. **性能优化**: 支持并行场景分割
2. **智能检测**: 集成 AI 模型进行更精确的场景检测
3. **格式支持**: 支持更多输出格式
4. **预览功能**: 生成场景预览图
5. **批量操作**: 支持批量场景分割和管理

## 总结

通过这次修复和集成：

1. **解决了关键错误**: 修复了 PySceneDetect API 变化导致的错误
2. **改进了用户体验**: 场景分割现在自动执行，无需手动触发
3. **增强了系统功能**: 基础分析现在包含完整的场景处理
4. **提供了灵活配置**: 用户可以根据需要调整场景检测和分割参数

这些改进使得视频分析系统更加完整和用户友好，为后续的高级分析功能奠定了基础。
