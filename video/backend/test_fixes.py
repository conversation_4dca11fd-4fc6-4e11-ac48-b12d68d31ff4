#!/usr/bin/env python3
"""
Test script to verify the fixes for the two issues:
1. split_video_ffmpeg parameter fix
2. HEAD method support for scene file endpoint
"""

import sys
import os

# Add the app directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_split_video_ffmpeg_import():
    """Test that split_video_ffmpeg can be imported and has correct signature"""
    try:
        from scenedetect.video_splitter import split_video_ffmpeg
        import inspect
        
        # Get function signature
        sig = inspect.signature(split_video_ffmpeg)
        params = list(sig.parameters.keys())
        
        print("✓ split_video_ffmpeg imported successfully")
        print(f"Function parameters: {params}")
        
        # Check if the first parameter is input_video_path (not input_video_paths)
        if params[0] == 'input_video_path':
            print("✓ First parameter is 'input_video_path' (correct)")
            return True
        else:
            print(f"✗ First parameter is '{params[0]}' (should be 'input_video_path')")
            return False
            
    except ImportError as e:
        print(f"✗ Failed to import split_video_ffmpeg: {e}")
        return False
    except Exception as e:
        print(f"✗ Error checking split_video_ffmpeg: {e}")
        return False

def test_scene_detection_service():
    """Test that SceneDetectionService can be imported"""
    try:
        from app.services.scene_detection_service import SceneDetectionService
        print("✓ SceneDetectionService imported successfully")
        return True
    except ImportError as e:
        print(f"✗ Failed to import SceneDetectionService: {e}")
        return False
    except Exception as e:
        print(f"✗ Error importing SceneDetectionService: {e}")
        return False

def test_fastapi_routes():
    """Test that FastAPI routes can be imported"""
    try:
        from app.api.v1.endpoints.videos import router
        print("✓ Video routes imported successfully")
        
        # Check if HEAD method is supported
        routes = router.routes
        scene_file_routes = [r for r in routes if hasattr(r, 'path') and 'scenes' in r.path and 'file' in r.path]
        
        if scene_file_routes:
            route = scene_file_routes[0]
            methods = getattr(route, 'methods', set())
            print(f"Scene file route methods: {methods}")
            
            if 'HEAD' in methods:
                print("✓ HEAD method is supported for scene file endpoint")
                return True
            else:
                print("✗ HEAD method is not supported for scene file endpoint")
                return False
        else:
            print("✗ Scene file route not found")
            return False
            
    except ImportError as e:
        print(f"✗ Failed to import video routes: {e}")
        return False
    except Exception as e:
        print(f"✗ Error checking video routes: {e}")
        return False

def main():
    """Run all tests"""
    print("Testing fixes for the two reported issues...\n")
    
    print("1. Testing split_video_ffmpeg parameter fix:")
    test1_passed = test_split_video_ffmpeg_import()
    print()
    
    print("2. Testing SceneDetectionService import:")
    test2_passed = test_scene_detection_service()
    print()
    
    print("3. Testing FastAPI HEAD method support:")
    test3_passed = test_fastapi_routes()
    print()
    
    if test1_passed and test2_passed and test3_passed:
        print("✓ All tests passed! The fixes should work correctly.")
        return 0
    else:
        print("✗ Some tests failed. Please check the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
