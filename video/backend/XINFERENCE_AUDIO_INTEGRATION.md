# Xinference Audio 服务集成指南

## 概述

本文档介绍如何在视频分析系统中集成和使用 Xinference Audio 服务进行语音识别和字幕生成。

## 配置

### 1. 环境配置

在 `.env` 文件中配置 Xinference 服务：

```bash
# Xinference 配置
XINFERENCE_ENABLED=true
XINFERENCE_BASE_URL=http://100.124.255.81:9997
XINFERENCE_API_KEY=
XINFERENCE_TIMEOUT=30
XINFERENCE_MAX_RETRIES=3

# 默认模型配置
XINFERENCE_DEFAULT_AUDIO=paraformer-zh-spk
```

### 2. 模型配置

系统支持以下音频模型：

- `paraformer-zh-spk`: Paraformer 中文语音识别模型（默认）
- `whisper-large-v3`: Whisper Large V3 多语言模型
- `whisper-base`: Whisper Base 多语言模型

## 功能特性

### 1. 自动服务选择

系统会根据配置自动选择使用 Xinference 或本地音频服务：

```python
# 在 SubtitleService 中
def _should_use_xinference(self) -> bool:
    return settings.XINFERENCE_ENABLED
```

### 2. 字幕生成

支持两种字幕生成方式：

#### 方式一：通过 SubtitleService（推荐）

```python
from app.services.subtitle_service import SubtitleService

subtitle_service = SubtitleService(db)
subtitle = subtitle_service.generate_automatic_subtitle(video_id)
```

#### 方式二：直接使用 XinferenceSubtitleService

```python
from app.services.xinference_subtitle_service import get_xinference_subtitle_service

xinference_service = get_xinference_subtitle_service(db)
subtitle = await xinference_service.generate_automatic_subtitle(video_id)
```

### 3. 音频转录

直接转录音频文件：

```python
from app.services.xinference_audio_service import XinferenceAudioService

audio_service = XinferenceAudioService()
result = await audio_service.transcribe_file(audio_file_path, language="zh")
```

## API 端点

### 1. 字幕生成

```bash
# 使用默认服务（自动选择 Xinference 或本地）
POST /api/v1/videos/{video_id}/generate-subtitle

# 强制使用 Xinference 服务
POST /api/v1/xinference/audio/generate-subtitle
```

### 2. 音频转录

```bash
# 转录上传的音频文件
POST /api/v1/xinference/audio/transcribe

# 转录视频中的音频
POST /api/v1/xinference/audio/transcribe-video
```

### 3. 模型管理

```bash
# 查看可用模型
GET /api/v1/xinference/models/available

# 查看运行中的模型
GET /api/v1/xinference/models/running

# 启动模型
POST /api/v1/xinference/models/{model_name}/launch

# 停止模型
DELETE /api/v1/xinference/models/{model_uid}
```

## 使用示例

### 1. 生成视频字幕

```bash
curl -X POST "http://localhost:8000/api/v1/videos/1/generate-subtitle" \
  -H "Content-Type: application/json"
```

### 2. 使用 Xinference 生成字幕

```bash
curl -X POST "http://localhost:8000/api/v1/xinference/audio/generate-subtitle" \
  -F "video_id=1" \
  -F "language=zh"
```

### 3. 转录音频文件

```bash
curl -X POST "http://localhost:8000/api/v1/xinference/audio/transcribe" \
  -F "audio_file=@test.wav" \
  -F "language=zh"
```

## 测试

### 1. 运行集成测试

```bash
cd video/backend
python test_xinference_audio.py
```

### 2. 测试内容

- Xinference 服务连接测试
- 音频模型启动测试
- 音频转录功能测试
- 字幕生成功能测试

## 故障排除

### 1. 连接问题

如果无法连接到 Xinference 服务：

1. 检查 `XINFERENCE_BASE_URL` 配置
2. 确认 Xinference 服务正在运行
3. 检查网络连接和防火墙设置

### 2. 模型启动失败

如果音频模型启动失败：

1. 检查模型名称是否正确
2. 确认 Xinference 服务有足够的资源
3. 查看 Xinference 服务日志

### 3. 转录质量问题

如果转录质量不佳：

1. 尝试使用不同的音频模型
2. 检查音频文件质量和格式
3. 调整语言参数设置

## 性能优化

### 1. 模型预加载

配置自动启动常用模型：

```python
# 在 xinference_config.py 中
ModelConfig(
    name="paraformer-zh-spk",
    model_type=ModelType.AUDIO,
    auto_launch=True  # 自动启动
)
```

### 2. 并发控制

配置最大并发请求数：

```bash
XINFERENCE_MAX_CONCURRENT_REQUESTS=5
```

### 3. 缓存配置

启用结果缓存：

```bash
XINFERENCE_CACHE_ENABLED=true
XINFERENCE_CACHE_TTL=3600
```

## 监控和日志

### 1. 健康检查

```bash
curl http://localhost:8000/api/v1/xinference/health
```

### 2. 日志查看

系统使用 loguru 记录详细日志：

```python
from loguru import logger

logger.info("Xinference audio transcription completed")
```

### 3. 性能监控

系统会记录处理时间和性能指标：

- 语音识别耗时
- 模型启动时间
- 文件处理速度

## 扩展功能

### 1. 批量处理

支持批量转录多个音频文件：

```bash
POST /api/v1/xinference/batch/transcribe
```

### 2. 语义搜索

结合 embedding 模型进行字幕内容搜索：

```bash
POST /api/v1/xinference/embedding/search
```

### 3. 多语言支持

支持多种语言的语音识别：

- 中文 (zh)
- 英文 (en)
- 其他 Whisper 支持的语言

## 最佳实践

1. **模型选择**: 中文内容优先使用 paraformer，多语言内容使用 whisper
2. **资源管理**: 定期清理空闲模型释放资源
3. **错误处理**: 实现降级机制，Xinference 不可用时自动切换到本地服务
4. **监控告警**: 监控服务状态和性能指标
5. **数据安全**: 注意音频数据的隐私保护
