# Xinference 集成架构设计

## 概述

本文档描述了如何在视频分析系统中集成 Xinference，提供通用化的多模型支持，包括 LLM、Audio、Embedding、Image、Rerank 等模型类型。

## 架构设计

### 1. 整体架构

```mermaid
graph TB
    subgraph "应用层"
        API[API 端点]
        WEB[Web 界面]
    end
    
    subgraph "服务层"
        VS[视频分析服务]
        AS[音频处理服务]
        TS[文本分析服务]
        IS[图像分析服务]
    end
    
    subgraph "Xinference 集成层"
        XC[Xinference 客户端]
        LLM[LLM 服务]
        AUDIO[Audio 服务]
        EMB[Embedding 服务]
        IMG[Image 服务]
        RANK[Rerank 服务]
    end
    
    subgraph "配置层"
        CONFIG[配置管理]
        MODEL_REG[模型注册]
    end
    
    subgraph "Xinference 服务"
        XINF[Xinference Server]
    end
    
    API --> VS
    API --> AS
    VS --> LLM
    VS --> IMG
    AS --> AUDIO
    TS --> LLM
    TS --> EMB
    TS --> RANK
    
    XC --> LLM
    XC --> AUDIO
    XC --> EMB
    XC --> IMG
    XC --> RANK
    
    XC --> XINF
    CONFIG --> XC
    MODEL_REG --> XC
```

### 2. 核心组件

#### 2.1 Xinference 客户端服务 (XinferenceClientService)
- 统一的 Xinference 连接管理
- 模型生命周期管理（启动、停止、监控）
- 连接池和重试机制
- 错误处理和日志记录

#### 2.2 模型类型服务
- **LLM 服务**: 大语言模型调用，支持对话、生成、工具调用
- **Audio 服务**: 语音识别、语音合成
- **Embedding 服务**: 文本向量化、相似度计算
- **Image 服务**: 文生图、图像分析
- **Rerank 服务**: 文本重排序、相关性评分

#### 2.3 配置管理
- Xinference 服务地址配置
- 模型配置和参数
- 认证信息管理
- 性能参数调优

### 3. 配置结构

```yaml
xinference:
  # 服务配置
  server:
    base_url: "http://localhost:9997"
    api_key: ""
    timeout: 30
    max_retries: 3
    
  # 模型配置
  models:
    llm:
      default: "glm4-chat"
      available:
        - name: "glm4-chat"
          engine: "llama.cpp"
          format: "ggufv2"
          size: 9
          quantization: "Q4_K"
        - name: "qwen2.5-chat"
          engine: "transformers"
          
    audio:
      default: "whisper-large-v3"
      available:
        - name: "whisper-large-v3"
          multilingual: true
        - name: "whisper-base"
          multilingual: true
          
    embedding:
      default: "bge-large-zh-v1.5"
      available:
        - name: "bge-large-zh-v1.5"
          dimensions: 1024
        - name: "bge-small-en-v1.5"
          dimensions: 384
          
    image:
      default: "stable-diffusion-xl-base-1.0"
      available:
        - name: "stable-diffusion-xl-base-1.0"
          family: "stable_diffusion"
          
    rerank:
      default: "bge-reranker-large"
      available:
        - name: "bge-reranker-large"
```

### 4. 使用场景

#### 4.1 视频分析场景
- **场景描述**: 使用 LLM 分析视频帧内容
- **字幕生成**: 使用 Audio 模型进行语音识别
- **内容摘要**: 使用 LLM 生成视频内容摘要
- **相似度检索**: 使用 Embedding 进行视频片段相似度计算

#### 4.2 音频处理场景
- **语音转文字**: 使用 Whisper 系列模型
- **多语言支持**: 根据音频语言选择合适的模型
- **实时转录**: 支持流式音频处理

#### 4.3 文本分析场景
- **内容理解**: 使用 LLM 进行文本分析
- **语义搜索**: 使用 Embedding 进行语义相似度计算
- **内容排序**: 使用 Rerank 进行相关性排序

### 5. API 设计

#### 5.1 模型管理 API
```
GET    /api/v1/xinference/models                    # 列出所有可用模型
POST   /api/v1/xinference/models/{model_name}/launch # 启动模型
DELETE /api/v1/xinference/models/{model_uid}         # 停止模型
GET    /api/v1/xinference/models/{model_uid}/status  # 获取模型状态
```

#### 5.2 模型调用 API
```
POST   /api/v1/xinference/llm/chat                   # LLM 对话
POST   /api/v1/xinference/llm/generate               # LLM 生成
POST   /api/v1/xinference/audio/transcribe           # 语音转文字
POST   /api/v1/xinference/embedding/create           # 创建向量
POST   /api/v1/xinference/image/generate             # 生成图像
POST   /api/v1/xinference/rerank/score               # 重排序评分
```

### 6. 错误处理

#### 6.1 连接错误
- 自动重试机制
- 降级策略（使用本地模型）
- 错误日志记录

#### 6.2 模型错误
- 模型启动失败处理
- 推理超时处理
- 资源不足处理

### 7. 性能优化

#### 7.1 连接池管理
- 复用 HTTP 连接
- 异步请求处理
- 并发控制

#### 7.2 模型缓存
- 模型实例缓存
- 结果缓存机制
- 内存管理

### 8. 监控和日志

#### 8.1 性能监控
- 请求响应时间
- 模型资源使用情况
- 错误率统计

#### 8.2 日志记录
- 请求日志
- 错误日志
- 性能日志

## 实现计划

1. **配置管理**: 扩展现有配置系统，添加 Xinference 相关配置
2. **客户端服务**: 实现通用的 Xinference 客户端服务
3. **模型服务**: 为每种模型类型实现专门的服务类
4. **API 端点**: 创建 Xinference 相关的 API 端点
5. **集成现有服务**: 将 Xinference 集成到现有的分析服务中
6. **测试和文档**: 编写测试用例和使用文档

## 使用示例

### 1. 启用 Xinference

在 `.env` 文件中配置：

```bash
# 启用 Xinference
XINFERENCE_ENABLED=true
XINFERENCE_BASE_URL=http://localhost:9997
XINFERENCE_API_KEY=your_api_key_here

# 默认模型配置
XINFERENCE_DEFAULT_LLM=glm4-chat
XINFERENCE_DEFAULT_AUDIO=whisper-large-v3
XINFERENCE_DEFAULT_EMBEDDING=bge-large-zh-v1.5
```

### 2. API 调用示例

#### 模型管理
```bash
# 健康检查
curl http://localhost:8000/api/v1/xinference/health

# 列出可用模型
curl http://localhost:8000/api/v1/xinference/models/available

# 启动模型
curl -X POST http://localhost:8000/api/v1/xinference/models/glm4-chat/launch

# 列出运行中的模型
curl http://localhost:8000/api/v1/xinference/models/running
```

#### LLM 调用
```bash
# 文本生成
curl -X POST http://localhost:8000/api/v1/xinference/llm/generate \
  -F "prompt=请介绍一下人工智能" \
  -F "max_tokens=200"

# 对话
curl -X POST http://localhost:8000/api/v1/xinference/llm/chat \
  -F 'messages=[{"role":"user","content":"你好"}]'
```

#### 音频转录
```bash
# 上传音频文件转录
curl -X POST http://localhost:8000/api/v1/xinference/audio/transcribe \
  -F "audio_file=@audio.wav" \
  -F "language=zh"

# 转录视频音频
curl -X POST http://localhost:8000/api/v1/xinference/audio/transcribe-video \
  -F "video_path=/path/to/video.mp4"
```

#### 向量化
```bash
# 创建文本向量
curl -X POST http://localhost:8000/api/v1/xinference/embedding/create \
  -F "text=这是一段测试文本"

# 计算相似度
curl -X POST http://localhost:8000/api/v1/xinference/embedding/similarity \
  -F "text1=苹果是一种水果" \
  -F "text2=橙子也是水果"
```

### 3. 视频分析集成

#### 综合视频分析
```bash
curl -X POST http://localhost:8000/api/v1/videos/123/xinference/comprehensive-analysis \
  -F "use_xinference=true" \
  -F "include_audio=true" \
  -F "include_visual=true" \
  -F "include_scenes=true" \
  -F "max_frames=10"
```

#### 视频转录
```bash
curl -X POST http://localhost:8000/api/v1/videos/123/xinference/transcribe \
  -F "use_xinference=true" \
  -F "language=zh"
```

#### 转录服务比较
```bash
curl -X POST http://localhost:8000/api/v1/videos/123/xinference/compare-transcription \
  -F "language=zh"
```

### 4. Python 代码示例

```python
from app.services.xinference_service import xinference_service

# 初始化服务
await xinference_service.initialize()

# LLM 对话
response = await xinference_service.chat([
    {"role": "user", "content": "请介绍一下深度学习"}
])

# 音频转录
transcription = await xinference_service.transcribe_audio(
    "audio.wav", language="zh"
)

# 文本向量化
embedding = await xinference_service.create_embedding(
    "这是一段测试文本"
)

# 语义搜索
search_results = await xinference_service.semantic_search(
    query="人工智能",
    documents=[
        {"content": "机器学习是人工智能的一个分支"},
        {"content": "深度学习使用神经网络"},
        {"content": "自然语言处理处理文本数据"}
    ]
)
```

## 测试

### 运行集成测试

```bash
cd video/backend
python test_xinference_integration.py
```

测试包括：
- 配置验证
- 健康检查
- 模型注册表
- 模型管理
- LLM 服务
- 音频服务
- 向量化服务

### 性能测试

```bash
# 测试并发请求
ab -n 100 -c 10 http://localhost:8000/api/v1/xinference/health

# 测试大文件转录
curl -X POST http://localhost:8000/api/v1/xinference/audio/transcribe \
  -F "audio_file=@large_audio.wav" \
  --max-time 300
```

## 故障排除

### 常见问题

1. **连接失败**
   - 检查 Xinference 服务是否运行
   - 验证 `XINFERENCE_BASE_URL` 配置
   - 检查网络连接

2. **模型启动失败**
   - 检查模型名称是否正确
   - 验证系统资源（内存、GPU）
   - 查看 Xinference 服务日志

3. **API 调用超时**
   - 增加 `XINFERENCE_TIMEOUT` 设置
   - 检查模型是否已启动
   - 验证请求参数

### 日志查看

```bash
# 查看应用日志
tail -f logs/app.log

# 查看 Xinference 服务日志
xinference logs
```

## 兼容性

- 保持与现有 MiniCPM-V-4 服务的兼容性
- 支持渐进式迁移
- 提供配置开关，可选择使用 Xinference 或本地模型
- 支持服务降级，Xinference 不可用时自动使用本地服务
