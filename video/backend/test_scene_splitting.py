#!/usr/bin/env python3
"""
测试场景分割功能的修复
"""

import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "app"))

def test_split_video_ffmpeg_signature():
    """测试 split_video_ffmpeg 函数签名"""
    print("🔍 Testing split_video_ffmpeg function signature...")
    try:
        from scenedetect.video_splitter import split_video_ffmpeg
        import inspect
        
        # 获取函数签名
        sig = inspect.signature(split_video_ffmpeg)
        params = list(sig.parameters.keys())
        
        print(f"✅ Function parameters: {params}")
        
        # 检查第一个参数
        if params[0] == 'input_video_path':
            print("✅ First parameter is 'input_video_path' (correct)")
        else:
            print(f"❌ First parameter is '{params[0]}' (should be 'input_video_path')")
            return False
        
        # 检查返回值注解
        return_annotation = sig.return_annotation
        print(f"✅ Return type annotation: {return_annotation}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Failed to import split_video_ffmpeg: {e}")
        return False
    except Exception as e:
        print(f"❌ Error checking split_video_ffmpeg: {e}")
        return False


def test_scene_detection_service_import():
    """测试场景检测服务导入"""
    print("🔍 Testing SceneDetectionService import...")
    try:
        from app.services.scene_detection_service import SceneDetectionService
        print("✅ SceneDetectionService imported successfully")
        
        # 检查方法签名
        import inspect
        
        # 检查 split_video_by_scenes 方法
        split_method = getattr(SceneDetectionService, 'split_video_by_scenes')
        split_sig = inspect.signature(split_method)
        split_params = list(split_sig.parameters.keys())
        print(f"✅ split_video_by_scenes parameters: {split_params}")
        
        # 检查 _save_scene_clips_to_db 方法
        save_method = getattr(SceneDetectionService, '_save_scene_clips_to_db')
        save_sig = inspect.signature(save_method)
        save_params = list(save_sig.parameters.keys())
        print(f"✅ _save_scene_clips_to_db parameters: {save_params}")
        
        # 确认 output_files 参数已被移除
        if 'output_files' not in save_params:
            print("✅ output_files parameter correctly removed from _save_scene_clips_to_db")
        else:
            print("❌ output_files parameter still present in _save_scene_clips_to_db")
            return False
        
        return True
        
    except ImportError as e:
        print(f"❌ Failed to import SceneDetectionService: {e}")
        return False
    except Exception as e:
        print(f"❌ Error checking SceneDetectionService: {e}")
        return False


def test_config_settings():
    """测试配置设置"""
    print("🔍 Testing configuration settings...")
    try:
        from app.core.config import settings
        
        # 检查场景检测相关配置
        scene_configs = [
            'SCENE_DETECTION_THRESHOLD',
            'SCENE_MIN_LENGTH', 
            'SCENE_DETECTION_ENABLED',
            'SCENE_AUTO_SPLIT_ENABLED',
            'SCENE_SPLIT_FORMAT'
        ]
        
        for config in scene_configs:
            if hasattr(settings, config):
                value = getattr(settings, config)
                print(f"✅ {config}: {value}")
            else:
                print(f"❌ Missing config: {config}")
                return False
        
        return True
        
    except ImportError as e:
        print(f"❌ Failed to import settings: {e}")
        return False
    except Exception as e:
        print(f"❌ Error checking settings: {e}")
        return False


def test_basic_info_task_import():
    """测试基础信息任务导入"""
    print("🔍 Testing basic info task import...")
    try:
        from app.tasks.video_tasks_basic_info import analyze_video_basic_info
        print("✅ analyze_video_basic_info imported successfully")
        
        # 检查函数签名
        import inspect
        sig = inspect.signature(analyze_video_basic_info)
        params = list(sig.parameters.keys())
        print(f"✅ analyze_video_basic_info parameters: {params}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Failed to import analyze_video_basic_info: {e}")
        return False
    except Exception as e:
        print(f"❌ Error checking analyze_video_basic_info: {e}")
        return False


def test_mock_scene_splitting():
    """模拟测试场景分割逻辑"""
    print("🔍 Testing mock scene splitting logic...")
    try:
        # 模拟 split_video_ffmpeg 的返回值
        mock_return_code = 0  # 成功
        
        # 测试返回码检查逻辑
        if mock_return_code != 0:
            raise RuntimeError(f"FFmpeg failed with return code {mock_return_code}")
        
        print("✅ Return code check logic works correctly")
        
        # 模拟场景列表
        mock_scenes = [
            {"scene_number": 1, "start_time": 0.0, "end_time": 10.0, "duration": 10.0},
            {"scene_number": 2, "start_time": 10.0, "end_time": 20.0, "duration": 10.0},
        ]
        
        # 测试场景数据处理
        if len(mock_scenes) > 0:
            print(f"✅ Mock scene processing: {len(mock_scenes)} scenes")
        
        return True
        
    except Exception as e:
        print(f"❌ Mock scene splitting test failed: {e}")
        return False


def main():
    """运行所有测试"""
    print("🧪 Testing scene splitting fixes...\n")
    
    tests = [
        ("split_video_ffmpeg signature", test_split_video_ffmpeg_signature),
        ("SceneDetectionService import", test_scene_detection_service_import),
        ("Configuration settings", test_config_settings),
        ("Basic info task import", test_basic_info_task_import),
        ("Mock scene splitting logic", test_mock_scene_splitting),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Running: {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ Test {test_name} crashed: {e}")
            results[test_name] = False
    
    # 总结结果
    print(f"\n{'='*50}")
    print("TEST SUMMARY")
    print('='*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Scene splitting fixes are working correctly.")
        return 0
    else:
        print("⚠️ Some tests failed. Check the output above.")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
