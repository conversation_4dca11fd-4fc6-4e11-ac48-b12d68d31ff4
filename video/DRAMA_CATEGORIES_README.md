# 短剧分类系统

## 概述

本系统实现了基于配置文件的短剧分类管理，支持动态加载和更新分类数据。

## 功能特性

### 1. 任务元数据管理
- **短剧简介** (synopsis): 文本字段，支持多行输入
- **短剧类型** (genre): 从预定义分类中选择，支持分组显示
- **短剧主题** (theme): 支持从预设主题选择或自定义输入

### 2. 全帧提取
- 修改了视频处理逻辑，从只提取关键帧改为提取每一帧
- 提供更完整的视频内容分析基础

## 配置文件

### 后端配置
- **文件位置**: `video/backend/app/config/drama_categories.py`
- **包含内容**:
  - `DRAMA_GENRES`: 所有短剧类型列表
  - `DRAMA_GENRE_GROUPS`: 按类别分组的类型
  - `DRAMA_THEMES`: 常用主题标签

### 前端配置
- **文件位置**: `video/frontend/src/config/dramaCategories.js`
- **包含内容**: 与后端配置相同的数据结构

## API接口

### 获取分类数据
```
GET /api/v1/tasks/categories
```

返回格式：
```json
{
  "genres": ["大女主", "总裁", "逆袭", ...],
  "genre_groups": {
    "爱情类": ["现代言情", "古代言情", ...],
    "都市类": ["都市", "都市日常", ...],
    ...
  },
  "themes": ["逆袭", "打脸虐渣", ...]
}
```

### 创建任务（支持新字段）
```
POST /api/v1/tasks
```

表单数据：
- `name`: 任务名称
- `description`: 任务描述
- `synopsis`: 短剧简介
- `genre`: 短剧类型
- `theme`: 短剧主题
- `config`: 任务配置

### 更新任务（支持新字段）
```
PUT /api/v1/tasks/{task_id}
```

## 数据库变更

### 新增字段
在 `tasks` 表中添加了以下字段：
- `synopsis` (TEXT): 短剧简介
- `genre` (VARCHAR(100)): 短剧类型
- `theme` (VARCHAR(200)): 短剧主题

### 迁移文件
- **文件**: `video/backend/alembic/versions/0007_add_task_drama_metadata.py`
- **运行**: `alembic upgrade head`

## 前端组件

### 任务创建表单
- **文件**: `video/frontend/src/views/TaskManagement/TaskList.vue`
- **功能**: 创建任务时可填写短剧元数据

### 任务详情页面
- **文件**: `video/frontend/src/views/TaskManagement/TaskDetail.vue`
- **功能**: 
  - 显示短剧元数据信息
  - 支持编辑短剧信息
  - 分组显示类型选择

## 如何添加新分类

### 方法1: 修改配置文件
1. 编辑 `video/backend/app/config/drama_categories.py`
2. 编辑 `video/frontend/src/config/dramaCategories.js`
3. 重启应用

### 方法2: 通过API动态加载（推荐）
1. 修改 `video/duanju_categories.json`
2. 更新后端配置加载逻辑
3. 前端会自动从API获取最新分类

## 注意事项

1. **存储空间**: 全帧提取会显著增加存储需求
2. **处理时间**: 提取所有帧会增加处理时间
3. **分类一致性**: 确保前后端配置文件保持同步
4. **向后兼容**: 保留了原有的关键帧提取方法作为兼容性包装器

## 使用示例

### 创建带分类的任务
```javascript
const taskData = {
  name: "测试短剧",
  description: "这是一个测试任务",
  synopsis: "一个关于爱情与成长的故事...",
  genre: "现代言情",
  theme: "逆袭"
}

await taskStore.createTask(taskData)
```

### 更新任务分类
```javascript
const updates = {
  genre: "都市",
  theme: "女性成长"
}

await taskStore.updateTask(taskId, updates)
```
